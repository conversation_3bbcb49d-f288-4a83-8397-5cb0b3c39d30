// 测试最终修复的批量停止功能
const axios = require('axios');
const mysql = require('mysql2/promise');

async function testFinalBatchStop() {
    console.log('🧪 测试最终修复的批量停止功能...');
    
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'autojs_control',
        password: 'root',
        database: 'autojs_control'
    });
    
    try {
        // 1. 创建一个正在执行的任务
        console.log('📝 创建正在执行的任务...');
        
        const taskId = `xianyu_keywordMessage_batch_${Date.now()}_device_192_168_1_71`;
        
        const [result] = await connection.execute(`
            INSERT INTO xianyu_execution_logs (
                task_id, device_id, device_name, function_type, function_name,
                execution_status, config_params, started_at, user_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?)
        `, [
            taskId,
            'device_192_168_1_71',
            'XT2343-3_192168171',
            'keywordMessage',
            '关键词私信',
            'running',
            JSON.stringify({
                keyword: '测试关键词',
                message: '测试私信内容',
                targetCount: 5
            }),
            1
        ]);
        
        const logId = result.insertId;
        console.log('✅ 任务创建成功，ID:', logId, 'TaskID:', taskId);
        
        // 2. 测试批量停止API
        console.log('\n🛑 测试批量停止API...');
        
        const stopResponse = await axios.post('http://localhost:3002/api/xianyu/stop', {
            deviceId: 'device_192_168_1_71'
            // 注意：没有传递taskId，模拟批量停止的情况
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.vVfyYDUWf1Gs71d4QH7hHbJjg04-DUDMcswHhlBK9Pc'
            }
        });
        
        console.log('📥 停止API响应:', stopResponse.data);
        
        // 3. 等待一段时间让数据库更新
        console.log('\n⏳ 等待数据库更新...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 4. 检查数据库状态是否更新
        console.log('\n🔍 检查数据库状态更新...');
        
        const [updatedLogs] = await connection.execute(
            'SELECT * FROM xianyu_execution_logs WHERE id = ?',
            [logId]
        );
        
        if (updatedLogs.length > 0) {
            const log = updatedLogs[0];
            console.log('📋 更新后的任务状态:', {
                id: log.id,
                taskId: log.task_id,
                executionStatus: log.execution_status,
                progressPercentage: log.progress_percentage,
                completedAt: log.completed_at,
                errorMessage: log.error_message,
                executionDuration: log.execution_duration
            });
            
            if (log.execution_status === 'stopped') {
                console.log('✅ 数据库状态更新成功！');
                console.log('✅ 批量停止功能工作正常');
            } else {
                console.log('❌ 数据库状态更新失败，当前状态:', log.execution_status);
            }
        }
        
        console.log('\n📝 修复总结:');
        console.log('问题根因：');
        console.log('1. ❌ xianyuLogService.getExecutionLogs 返回 { data: [...] }');
        console.log('2. ❌ 停止API访问 runningLogs.logs（应该是 runningLogs.data）');
        console.log('3. ❌ 数据结构字段映射错误');
        console.log('');
        console.log('修复方案：');
        console.log('1. ✅ 修正访问字段：runningLogs.logs -> runningLogs.data');
        console.log('2. ✅ 修正数据库更新：使用 task_id 字段查找记录');
        console.log('3. ✅ 添加详细调试日志');
        
        // 5. 清理测试数据
        console.log('\n🧹 清理测试数据...');
        await connection.execute('DELETE FROM xianyu_execution_logs WHERE id = ?', [logId]);
        
        console.log('✅ 批量停止修复测试完成');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.response?.data || error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    } finally {
        await connection.end();
    }
}

testFinalBatchStop();
