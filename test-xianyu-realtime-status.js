// 测试闲鱼实时状态上报
const axios = require('axios');

async function testRealtimeStatus() {
    console.log('🧪 开始测试闲鱼实时状态上报...');
    
    const testData = {
        deviceId: 'test_device_001',
        taskId: 'test_task_' + Date.now(),
        currentStatus: '测试状态',
        processedStepCount: 5,
        searchAttemptCount: 3,
        successCount: 2,
        failedCount: 1,
        timestamp: new Date().toISOString()
    };
    
    try {
        console.log('📤 发送测试数据:', testData);
        
        const response = await axios.post('http://localhost:3002/api/xianyu/realtime-status', testData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });
        
        console.log('✅ 响应状态:', response.status);
        console.log('✅ 响应数据:', response.data);
        
        // 发送多个状态更新来模拟脚本执行过程
        const statusUpdates = [
            { currentStatus: '正在启动闲鱼应用', processedStepCount: 1 },
            { currentStatus: '正在搜索关键词', processedStepCount: 2, searchAttemptCount: 1 },
            { currentStatus: '找到帖子，开始私信', processedStepCount: 3, successCount: 1 },
            { currentStatus: '私信发送成功', processedStepCount: 4, successCount: 2 },
            { currentStatus: '任务完成', processedStepCount: 5, successCount: 2, failedCount: 0 }
        ];
        
        for (let i = 0; i < statusUpdates.length; i++) {
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            
            const updateData = {
                ...testData,
                ...statusUpdates[i],
                timestamp: new Date().toISOString()
            };
            
            console.log(`📤 发送状态更新 ${i + 1}:`, updateData);
            
            const updateResponse = await axios.post('http://localhost:3002/api/xianyu/realtime-status', updateData, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            console.log(`✅ 状态更新 ${i + 1} 响应:`, updateResponse.data);
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('❌ 响应状态:', error.response.status);
            console.error('❌ 响应数据:', error.response.data);
        }
    }
}

// 运行测试
testRealtimeStatus().then(() => {
    console.log('🎉 测试完成');
    process.exit(0);
}).catch(error => {
    console.error('💥 测试异常:', error);
    process.exit(1);
});
