<template>
  <div class="xianyu-logs">
    <div class="page-header">
      <h2>闲鱼执行日志</h2>
      <p>查看闲鱼自动化工具的执行记录和详细日志</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="功能类型">
          <el-select v-model="filterForm.functionType" placeholder="全部功能" clearable>
            <el-option label="关键词私信" value="keywordMessage"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="设备">
          <el-select v-model="filterForm.deviceId" placeholder="全部设备" clearable>
            <el-option
              v-for="device in allDevices"
              :key="device.device_id"
              :label="device.device_name || device.device_id"
              :value="device.device_id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="执行状态">
          <el-select v-model="filterForm.executionStatus" placeholder="全部状态" clearable>
            <el-option label="等待中" value="waiting"></el-option>
            <el-option label="执行中" value="running"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="已失败" value="failed"></el-option>
            <el-option label="已停止" value="stopped"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadLogs">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="warning" @click="stopAllTasks" :loading="stoppingAll">
            <i class="el-icon-video-pause"></i> 终止所有任务
          </el-button>
          <el-button type="danger" @click="clearAllLogs" :loading="clearingLogs">
            <i class="el-icon-delete"></i> 清空日志
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 日志列表 -->
    <el-card class="logs-card">
      <el-table
        :data="logs"
        v-loading="loading"
        stripe
        style="width: 100%"
        :default-sort="{prop: 'createdAt', order: 'descending'}"
      >
        <el-table-column prop="id" label="ID" width="80" sortable></el-table-column>
        
        <el-table-column prop="functionType" label="功能类型" width="120">
          <template slot-scope="scope">
            <el-tag size="small">
              {{ getFunctionTypeName(scope.row.functionType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="deviceInfo" label="设备信息" width="150">
          <template slot-scope="scope">
            <div>
              <div>{{ scope.row.deviceInfo.name || scope.row.deviceInfo.id }}</div>
              <div style="color: #909399; font-size: 12px;">{{ scope.row.deviceInfo.ip }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="selectedApp" label="应用版本" width="120">
          <template slot-scope="scope">
            <el-tag
              size="small"
              type="info"
            >
              {{ scope.row.selectedApp || scope.row.configParams?.selectedApp || '默认' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="executionStatus" label="执行状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.executionStatus)"
              size="small"
            >
              {{ getStatusText(scope.row.executionStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="progress" label="进度" width="100">
          <template slot-scope="scope">
            <el-progress
              :percentage="scope.row.progress || 0"
              :status="getProgressStatus(scope.row.executionStatus)"
              :stroke-width="6"
            ></el-progress>
          </template>
        </el-table-column>

        <el-table-column prop="configParams" label="配置参数" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.configParams">
              <div><strong>关键词:</strong> {{ scope.row.configParams.keyword }}</div>
              <div><strong>私信内容:</strong> {{ scope.row.configParams.message }}</div>
              <div><strong>目标数量:</strong> {{ scope.row.configParams.targetCount }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="executionResult" label="执行结果" min-width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.executionResult">
              <div><strong>成功私信:</strong> {{ scope.row.executionResult.successCount || 0 }}</div>
              <div><strong>失败数量:</strong> {{ scope.row.executionResult.failureCount || 0 }}</div>
              <div v-if="scope.row.executionResult.errorMessage" style="color: #F56C6C;">
                <strong>错误:</strong> {{ scope.row.executionResult.errorMessage }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="executionTime" label="执行时长" width="100">
          <template slot-scope="scope">
            {{ formatDuration(scope.row.executionTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="160" sortable>
          <template slot-scope="scope">
            {{ formatTime(scope.row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.executionStatus === 'running'"
              type="danger"
              size="mini"
              @click="stopExecution(scope.row)"
            >
              停止
            </el-button>
            <el-button
              v-if="['completed', 'failed', 'stopped'].includes(scope.row.executionStatus)"
              type="primary"
              size="mini"
              @click="retryExecution(scope.row)"
            >
              再来一次
            </el-button>
            <el-button
              type="info"
              size="mini"
              @click="viewDetails(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="执行详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedLog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="功能类型">{{ getFunctionTypeName(selectedLog.functionType) }}</el-descriptions-item>
          <el-descriptions-item label="设备信息">{{ selectedLog.deviceInfo.name || selectedLog.deviceInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="执行状态">{{ getStatusText(selectedLog.executionStatus) }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ selectedLog.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="执行时长">{{ formatDuration(selectedLog.executionTime) }}</el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>配置参数</h4>
          <pre>{{ JSON.stringify(selectedLog.configParams, null, 2) }}</pre>
        </div>

        <div v-if="selectedLog.executionResult" style="margin-top: 20px;">
          <h4>执行结果</h4>
          <pre>{{ JSON.stringify(selectedLog.executionResult, null, 2) }}</pre>
        </div>

        <div v-if="selectedLog.debugLogs && selectedLog.debugLogs.length > 0" style="margin-top: 20px;">
          <h4>调试日志</h4>
          <div class="debug-logs">
            <div v-for="(log, index) in selectedLog.debugLogs" :key="index" class="debug-log-item">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'XianyuLogs',
  data() {
    return {
      logs: [],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      filterForm: {
        functionType: '',
        deviceId: '',
        executionStatus: ''
      },
      dateRange: null,
      detailDialogVisible: false,
      selectedLog: null,
      refreshTimer: null,
      stoppingAll: false,
      clearingLogs: false
    }
  },
  computed: {
    allDevices() {
      return this.$store.getters['device/devices'] || []
    }
  },
  async mounted() {
    await this.loadData()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  methods: {
    async loadData() {
      await this.$store.dispatch('device/fetchDevices')
      await this.loadLogs()
    },

    async loadLogs() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          ...this.filterForm
        }

        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }

        const response = await this.$http.get('/api/xianyu/logs', { params })

        if (response.data.success) {
          this.logs = response.data.data.data || []  // 修复数据结构访问
          this.total = response.data.data.total || 0
        } else {
          this.$message.error('加载日志失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载闲鱼执行日志失败:', error)
        this.$message.error('加载日志失败')
      } finally {
        this.loading = false
      }
    },

    resetFilter() {
      this.filterForm = {
        functionType: '',
        deviceId: '',
        executionStatus: ''
      }
      this.dateRange = null
      this.currentPage = 1
      this.loadLogs()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadLogs()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadLogs()
    },

    getFunctionTypeName(type) {
      const typeMap = {
        'keywordMessage': '关键词私信'
      }
      return typeMap[type] || type
    },

    getStatusText(status) {
      const statusMap = {
        'waiting': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '已失败',
        'stopped': '已停止'
      }
      return statusMap[status] || status
    },

    getStatusTagType(status) {
      const typeMap = {
        'waiting': 'info',
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'stopped': 'info'
      }
      return typeMap[status] || 'info'
    },

    getProgressStatus(status) {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return null
    },

    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    },

    formatDuration(seconds) {
      if (!seconds) return '-'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      if (hours > 0) {
        return `${hours}时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    },

    async stopExecution(log) {
      try {
        console.log('闲鱼日志: 停止执行任务', log)
        
        const response = await this.$http.post('/api/xianyu/stop', {
          logId: log.id,
          deviceId: log.deviceInfo.id
        })

        if (response.data.success) {
          this.$message.success('停止成功')

          // 重置前端Vuex状态
          console.log('闲鱼日志: 重置前端状态')
          this.$store.dispatch('xianyu/clearDeviceExecutionState', log.deviceInfo.id)

          // 🔧 强制重置所有闲鱼功能状态
          console.log('闲鱼日志: 强制重置所有功能状态')
          this.$store.dispatch('xianyu/resetAllStates')

          // 🔧 清理localStorage中的状态备份
          console.log('闲鱼日志: 清理localStorage状态备份')
          const backupKeys = [
            `xianyuKeywordMessage_backup_${log.deviceInfo.id}`,
            `xianyuKeywordMessage_state_${log.deviceInfo.id}`,
            `xianyu_execution_state_${log.deviceInfo.id}`,
            `xianyu_realtime_data_${log.deviceInfo.id}`
          ]

          backupKeys.forEach(key => {
            try {
              localStorage.removeItem(key)
              console.log(`闲鱼日志: 已清理localStorage键: ${key}`)
            } catch (error) {
              console.error(`闲鱼日志: 清理localStorage键失败: ${key}`, error)
            }
          })

          // 立即更新Vuex中的设备状态
          this.$store.dispatch('device/updateDeviceStatus', {
            deviceId: log.deviceInfo.id,
            status: 'online',
            lastSeen: new Date()
          })

          // 强制更新设备状态为online
          try {
            console.log('闲鱼日志: 强制更新设备状态为online')
            await this.$http.post('/api/device/force-status', {
              deviceId: log.deviceInfo.id,
              status: 'online'
            })
            console.log('闲鱼日志: 设备状态强制更新成功')
          } catch (error) {
            console.error('闲鱼日志: 设备状态强制更新失败:', error)
          }

          // 发送任务停止事件给其他页面
          this.$root.$emit('xianyu-task-stopped', {
            functionType: log.functionType,
            deviceId: log.deviceInfo.id,
            taskId: log.id,
            message: '任务已停止'
          })

          // 发送设备状态更新事件
          this.$root.$emit('device-status-updated', {
            deviceId: log.deviceInfo.id,
            status: 'online'
          })

          // 刷新日志列表
          await this.loadLogs()
        } else {
          this.$message.error('停止失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('停止执行失败:', error)
        this.$message.error('停止失败')
      }
    },

    retryExecution(log) {
      // 跳转到闲鱼自动化页面并传递重试参数
      this.$router.push({
        path: '/xianyu',
        query: {
          retry: 'true',
          functionType: log.functionType,
          deviceId: log.deviceInfo.id,
          config: JSON.stringify(log.configParams)
        }
      })
    },

    viewDetails(log) {
      this.selectedLog = log
      this.detailDialogVisible = true
    },

    handleDetailClose() {
      this.detailDialogVisible = false
      this.selectedLog = null
    },

    startAutoRefresh() {
      // 每6秒刷新一次
      this.refreshTimer = setInterval(() => {
        this.loadLogs()
      }, 6000)
    },

    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 终止所有任务
    async stopAllTasks() {
      this.$confirm('确定要终止所有正在执行的闲鱼任务吗？', '确认终止', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.stoppingAll = true
        try {
          const response = await this.$http.post('/api/xianyu/stop-all')

          if (response.data.success) {
            this.$message.success(response.data.message)
            
            // 重置所有前端Vuex状态
            console.log('闲鱼日志: 重置所有前端状态')
            this.$store.dispatch('xianyu/resetAllStates')
            
            // 强制更新所有忙碌设备的状态为online
            try {
              console.log('闲鱼日志: 强制更新所有忙碌设备状态为online')
              const busyDevices = this.allDevices.filter(device => device.status === 'busy')
              for (const device of busyDevices) {
                await this.$http.post('/api/device/force-status', {
                  deviceId: device.device_id,
                  status: 'online'
                })
                console.log(`闲鱼日志: 设备 ${device.device_id} 状态强制更新成功`)
              }
            } catch (error) {
              console.error('闲鱼日志: 设备状态强制更新失败:', error)
            }
            
            // 发送全局任务停止事件
            this.$root.$emit('xianyu-reset-all-states')
            
            // 刷新日志列表
            await this.loadLogs()
          } else {
            this.$message.error('终止失败: ' + response.data.message)
          }
        } catch (error) {
          console.error('终止所有任务失败:', error)
          this.$message.error('终止失败: ' + (error.response?.data?.message || error.message))
        } finally {
          this.stoppingAll = false
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 清空所有日志
    async clearAllLogs() {
      this.$confirm('确定要清空所有闲鱼执行日志吗？此操作不可恢复！', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.clearingLogs = true
        try {
          const response = await this.$http.delete('/api/xianyu/logs')

          if (response.data.success) {
            this.$message.success(response.data.message)
            // 清空当前列表
            this.logs = []
            this.total = 0
            this.currentPage = 1
          } else {
            this.$message.error('清空失败: ' + response.data.message)
          }
        } catch (error) {
          console.error('清空日志失败:', error)
          this.$message.error('清空失败: ' + (error.response?.data?.message || error.message))
        } finally {
          this.clearingLogs = false
        }
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style scoped>
.xianyu-logs {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  color: white;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.logs-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.debug-logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
}

.debug-log-item {
  margin-bottom: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #303133;
}

/* 表格样式优化 */
.el-table {
  font-size: 14px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xianyu-logs {
    padding: 10px;
  }

  .filter-card .el-form {
    display: block;
  }

  .filter-card .el-form-item {
    display: block;
    margin-bottom: 15px;
  }

  .pagination-wrapper {
    text-align: center;
  }
}
</style>
