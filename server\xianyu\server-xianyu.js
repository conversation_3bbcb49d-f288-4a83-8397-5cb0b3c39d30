/**
 * 服务器闲鱼自动化模块 - 第6个文件 (原始文件第8500-11400行)
 * 包含闲鱼自动化相关的所有API和功能
 */

const fs = require('fs');
const path = require('path');

// 通用的服务器地址获取函数
function getServerHost(req) {
  // 获取真实的服务器地址（手机端可以访问的地址）
  let serverHost = req.get('host') || 'localhost:3002';

  // 如果是localhost，尝试获取PC的真实IP地址
  if (serverHost.includes('localhost') || serverHost.includes('127.0.0.1')) {
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();

      // 查找第一个非回环的IPv4地址
      for (const interfaceName in networkInterfaces) {
          const addresses = networkInterfaces[interfaceName];
          for (const addr of addresses) {
              if (addr.family === 'IPv4' && !addr.internal) {
                  serverHost = `${addr.address}:3002`;
                  break;
              }
          }
          if (!serverHost.includes('localhost')) break;
      }
  }

  console.log(`闲鱼脚本服务器地址: ${serverHost}`);
  return serverHost;
}

// 通用的服务器地址替换函数
function replaceServerAddresses(script, req) {
  // 获取真实的服务器地址（手机端可以访问的地址）
  let serverHost = req.get('host') || 'localhost:3002';

  // 如果是localhost，尝试获取PC的真实IP地址
  if (serverHost.includes('localhost') || serverHost.includes('127.0.0.1')) {
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();

      // 查找第一个非回环的IPv4地址
      for (const interfaceName in networkInterfaces) {
          const addresses = networkInterfaces[interfaceName];
          for (const addr of addresses) {
              if (addr.family === 'IPv4' && !addr.internal) {
                  serverHost = `${addr.address}:3002`;
                  break;
              }
          }
          if (!serverHost.includes('localhost')) break;
      }
  }

  // 替换所有可能的服务器地址格式
  script = script.replace(/http:\/\/192\.168\.1\.91:3002/g, `http://${serverHost}`);
  script = script.replace(/192\.168\.1\.91:3002/g, serverHost);
  script = script.replace(/http:\/\/localhost:3002/g, `http://${serverHost}`);
  script = script.replace(/localhost:3002/g, serverHost);
  script = script.replace(/127\.0\.0\.1:3002/g, serverHost);

  console.log(`闲鱼脚本地址替换完成，目标地址: ${serverHost}`);
  return script;
}

// 闲鱼自动化模块设置函数
async function setupServerXianyu(app, io, coreData, authData) {
  console.log('🔧 设置闲鱼自动化模块...');

  // 引入用户隔离中间件和工具
  const { userIsolationMiddleware } = require('../middleware/userIsolation');
  const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
  const PermissionValidator = require('../utils/PermissionValidator');

  const {
    pool,
    devices, 
    webClients,
    pendingCommands,
    deviceCommands,
    xianyuLogService,
    xianyuChatService,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 更新设备状态函数
  async function updateDeviceStatus(deviceId, status) {
    console.log(`更新设备状态: ${deviceId} -> ${status}`);

    // 查找设备
    let device = null;
    for (const [socketId, deviceData] of devices) {
      if (deviceData.deviceId === deviceId) {
        device = deviceData;
        // 更新内存中的设备状态
        device.status = status;
        device.lastSeen = new Date();
        break;
      }
    }

    if (!device) {
      console.log(`设备未找到，无法更新状态: ${deviceId}`);
      return;
    }

    // 尝试更新数据库状态（如果可用）
    if (pool) {
      try {
        await pool.execute(`
          UPDATE devices SET status = ?, last_seen = NOW()
          WHERE device_id = ?
        `, [status, deviceId]);

        console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
      } catch (dbError) {
        console.error('数据库状态更新失败:', dbError);
      }
    }

    // 通过WebSocket广播设备状态更新
    io.emit('device_status_update', {
      deviceId: deviceId,
      status: status,
      lastSeen: new Date()
    });
  }

  // 闲鱼自动化任务存储
  let xianyuActiveTasks = new Map();
  let xianyuTaskHistory = [];

  // 闲鱼私聊记录存储（内存存储，用于数据库不可用时）
  const chatRecords = new Map();

  // 测试路由
  app.get('/api/xianyu/test', (req, res) => {
    res.json({
      success: true,
      message: '闲鱼自动化API正常工作',
      timestamp: new Date().toISOString(),
      routes: [
        'GET /api/xianyu/test',
        'POST /api/xianyu/execute',
        'POST /api/xianyu/stop',
        'GET /api/xianyu/tasks'
      ]
    });
  });

  // 闲鱼实时状态API（Web端使用，需要认证） - 已添加用户隔离
  app.post('/api/xianyu/realtime-status-web', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const {
        deviceId,
        taskId,
        sentMessageCount,
        processedCount,
        currentStatus,
        message,
        timestamp
      } = req.body;
      const userId = req.currentUserId;

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`📊 [闲鱼实时状态] 用户${userId}设备${deviceId}状态更新:`, {
        deviceId,
        taskId,
        sentMessageCount,
        processedCount,
        currentStatus,
        message
      });

      // 构造完整的状态数据
      const statusData = {
        deviceId,
        taskId,
        sentMessageCount,
        processedCount,
        currentStatus,
        message,
        timestamp: timestamp || new Date().toISOString()
      };

      // 广播实时状态到所有连接的客户端
      io.emit('xianyu_realtime_status', statusData);

      console.log(`✅ [闲鱼实时状态] 已广播`);

      res.json({
        success: true,
        message: '闲鱼实时状态更新成功'
      });

    } catch (error) {
      console.error('处理闲鱼实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 创建数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 执行闲鱼自动化任务 - 已添加用户隔离
  app.post('/api/xianyu/execute', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { functionType, deviceConfigs, deviceIds, taskId } = req.body;
      const userId = req.currentUserId;

      console.log(`🔍 用户${userId}发起闲鱼自动化执行请求:`, {
        function: functionType,
        devices: deviceIds ? deviceIds.length : 0,
        deviceConfigs: deviceConfigs ? Object.keys(deviceConfigs).length : 0,
        taskId: taskId
      });

      // 验证参数
      if (!functionType || !deviceIds || deviceIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 验证设备所属权
      console.log(`[闲鱼执行] 验证用户${userId}对设备的权限: ${deviceIds.join(', ')}`);
      const devicePermissions = await permissionValidator.validateBatchOwnership('device', deviceIds, userId);

      if (!devicePermissions.hasPermission) {
        console.log(`[闲鱼执行] 用户${userId}无权访问设备: ${devicePermissions.invalid.join(', ')}`);
        return res.status(403).json({
          success: false,
          message: `无权访问以下设备: ${devicePermissions.invalid.join(', ')}`
        });
      }

      // 根据功能类型选择对应的脚本文件
      const scriptMap = {
        'keywordMessage': '../xy-jb/闲鱼关键词私信-无ui界面.js'
      };

      const scriptPath = scriptMap[functionType];
      if (!scriptPath) {
        return res.status(400).json({
          success: false,
          message: '不支持的功能类型: ' + functionType
        });
      }

      // 检查脚本文件是否存在
      const fullScriptPath = path.resolve(__dirname, '..', scriptPath);
      if (!fs.existsSync(fullScriptPath)) {
        console.error(`脚本文件不存在: ${fullScriptPath}`);
        return res.status(404).json({
          success: false,
          message: `脚本文件不存在: ${scriptPath}`
        });
      }

      // 读取脚本内容
      let scriptContent;
      try {
        scriptContent = fs.readFileSync(fullScriptPath, 'utf8');
        console.log(`✅ 成功读取脚本文件: ${scriptPath}`);
      } catch (error) {
        console.error(`❌ 读取脚本文件失败: ${scriptPath}`, error);
        return res.status(500).json({
          success: false,
          message: `读取脚本文件失败: ${error.message}`
        });
      }

      let successCount = 0;
      let failCount = 0;
      const executionResults = [];

      // 为每个设备执行任务
      for (const deviceId of deviceIds) {
        try {
          // 查找设备
          let device = null;
          for (const [socketId, deviceData] of devices) {
            if (deviceData.deviceId === deviceId) {
              device = deviceData;
              break;
            }
          }

          if (!device) {
            console.log(`⚠️ 设备不存在或已离线: ${deviceId}`);
            failCount++;
            executionResults.push({
              deviceId,
              deviceName: '未知设备',
              status: 'not_found',
              message: '设备不存在或已离线'
            });
            continue;
          }

          // 获取设备配置
          const deviceConfig = deviceConfigs[deviceId] || {};
          console.log(`📱 设备 ${deviceId} 配置:`, deviceConfig);

          // 获取应用选择信息（与原始server.js保持一致）
          let appPackageName = 'com.taobao.idlefish'; // 默认包名
          let appCoordinates = { x: 270, y: 1350 }; // 默认坐标
          let appLaunchMethod = 'coordinate'; // 默认启动方法

          if (deviceConfig.selectedApp) {
            console.log('🔍 查找设备应用信息:', deviceId, deviceConfig.selectedApp);

            // 从数据库获取设备应用信息
            try {
              if (pool) {
                console.log('📊 查询参数:', { deviceId, selectedApp: deviceConfig.selectedApp });

                // 先查询该设备的所有闲鱼应用
                const [allApps] = await pool.execute(`
                  SELECT app_name, app_text, app_bounds, is_clickable, detection_method
                  FROM device_apps
                  WHERE device_id = ? AND app_type = 'xianyu'
                  ORDER BY detected_at DESC
                `, [deviceId]);

                console.log(`📱 设备 ${deviceId} 的所有闲鱼应用:`, allApps);

                const [rows] = await pool.execute(`
                  SELECT app_name, app_text, app_bounds, is_clickable, detection_method
                  FROM device_apps
                  WHERE device_id = ? AND app_type = 'xianyu' AND app_text = ?
                  ORDER BY detected_at DESC
                  LIMIT 1
                `, [deviceId, deviceConfig.selectedApp]);

                if (rows.length > 0) {
                  const appInfo = rows[0];
                  appPackageName = appInfo.app_name;

                  // 解析应用坐标信息（处理Rect格式字符串）
                  if (appInfo.app_bounds) {
                    try {
                      // 如果是JSON格式，直接解析
                      if (appInfo.app_bounds.startsWith('{')) {
                        appCoordinates = JSON.parse(appInfo.app_bounds);
                      } else {
                        // 如果是Rect格式，解析坐标
                        const rectMatch = appInfo.app_bounds.match(/Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)/);
                        if (rectMatch) {
                          const [, left, top, right, bottom] = rectMatch;
                          appCoordinates = {
                            x: Math.floor((parseInt(left) + parseInt(right)) / 2),
                            y: Math.floor((parseInt(top) + parseInt(bottom)) / 2)
                          };
                        } else {
                          appCoordinates = null;
                        }
                      }
                    } catch (e) {
                      console.log('⚠️ 解析应用坐标失败:', e.message);
                      appCoordinates = null;
                    }
                  } else {
                    appCoordinates = null;
                  }

                  appLaunchMethod = appInfo.detection_method === 'keyword' || appInfo.detection_method === 'regex' ? 'text' : 'coordinate';

                  console.log('✅ 从数据库获取到闲鱼应用信息:', {
                    selectedApp: deviceConfig.selectedApp,
                    packageName: appPackageName,
                    coordinates: appCoordinates,
                    launchMethod: appLaunchMethod,
                    detectionMethod: appInfo.detection_method
                  });
                } else {
                  console.log('⚠️ 数据库中未找到匹配的应用信息，使用默认配置');
                  console.log('🔍 查询条件:', { deviceId, app_type: 'xianyu', app_text: deviceConfig.selectedApp });
                  appPackageName = 'com.taobao.idlefish';
                  appCoordinates = { x: 270, y: 1350 };
                }
              }
            } catch (dbError) {
              console.error('❌ 查询数据库应用信息失败:', dbError);
              // 使用默认配置
              appPackageName = 'com.taobao.idlefish';
              appCoordinates = { x: 270, y: 1350 };
            }
          }

          // 生成唯一的任务ID
          // 如果前端传递了taskId，为每个设备生成基于该taskId的唯一ID
          const generatedTaskId = taskId ? `${taskId}_${deviceId}` : `xianyu_${functionType}_${Date.now()}_device_${deviceId.replace(/[^a-zA-Z0-9]/g, '_')}`;

          // 创建配置注入代码（包含应用信息）
          const enhancedDeviceConfig = {
            ...deviceConfig,
            selectedApp: deviceConfig.selectedApp || '',
            appCoordinates: appCoordinates,
            appPackageName: appPackageName,
            appLaunchMethod: appLaunchMethod
          };

          const configInjection = `
// === 闲鱼自动化配置注入 ===
// 由Web端自动注入的配置数据
var __webConfig = ${JSON.stringify(enhancedDeviceConfig, null, 2)};
var __taskId = "${generatedTaskId}";
var __functionType = "${functionType}";
var __deviceId = "${deviceId}";
var __serverHost = "${getServerHost(req)}";

// 兼容原始脚本的globalConfig变量
var globalConfig = {
    taskId: __taskId,
    deviceId: __deviceId,
    selectedApp: __webConfig.selectedApp || '',
    appCoordinates: __webConfig.appCoordinates || null,
    appPackageName: __webConfig.appPackageName || 'com.taobao.idlefish',
    appLaunchMethod: __webConfig.appLaunchMethod || 'coordinate'
};

console.log("=== 闲鱼自动化配置已注入 ===");
console.log("任务ID:", __taskId);
console.log("功能类型:", __functionType);
console.log("设备ID:", __deviceId);
console.log("服务器地址:", __serverHost);
console.log("globalConfig:", JSON.stringify(globalConfig, null, 2));
console.log("Web配置内容:", JSON.stringify(__webConfig, null, 2));

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
if (typeof sentMessageCount === 'undefined') {
    var sentMessageCount = 0;
}
if (typeof processedCount === 'undefined') {
    var processedCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount + ', sentMessageCount=' + sentMessageCount + ', processedCount=' + processedCount);

// 闲鱼实时状态上报函数（Auto.js兼容版本）
function sendXianyuRealtimeStatus(statusData) {
    try {
        // 构造要发送的数据
        const data = {
            deviceId: __deviceId,
            taskId: __taskId,
            sentMessageCount: statusData.sentMessageCount || 0,
            processedCount: statusData.processedCount || 0,
            currentStatus: statusData.currentStatus || '执行中',
            message: statusData.message || '',
            timestamp: new Date().toISOString()
        };

        // 手动合并statusData的属性（兼容旧版Auto.js）
        if (statusData) {
            for (let key in statusData) {
                if (statusData.hasOwnProperty(key)) {
                    data[key] = statusData[key];
                }
            }
        }

        // 使用线程发送HTTP请求到服务器（Auto.js方式）
        threads.start(function() {
            try {
                const response = http.postJson("http://" + __serverHost + "/api/xianyu/realtime-status", data, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 3000
                });

                if (response && response.statusCode === 200) {
                    console.log("闲鱼实时状态上报成功");
                } else {
                    console.log("闲鱼实时状态上报失败: " + (response ? response.statusCode : "无响应"));
                }
            } catch (e) {
                console.log("闲鱼实时状态上报网络错误: " + e.message);
            }
        });

        // 同时记录本地日志
        const logMessage = '闲鱼实时状态: ' + JSON.stringify(data);
        if (typeof addLog === 'function') {
            addLog(logMessage);
        } else {
            console.log(logMessage);
        }
    } catch (e) {
        // 静默处理错误
        console.log("闲鱼实时状态上报错误: " + e.message);
    }
}

// 注意：配置覆盖逻辑已移至脚本末尾执行

// 发送开始状态
sendXianyuRealtimeStatus({
    sentMessageCount: 0,
    processedCount: 0,
    currentStatus: '开始执行',
    message: '闲鱼脚本开始执行'
});

// 自动执行脚本
setTimeout(() => {
    console.log("开始自动执行闲鱼脚本");
    try {
        if (typeof main === 'function') {
            main();
        } else if (typeof startScript === 'function') {
            startScript();
        } else {
            console.log("脚本将自动开始执行");
        }

        // 发送执行完成状态到服务器
        console.log("闲鱼脚本执行完成");
        sendXianyuRealtimeStatus({
            sentMessageCount: sentMessageCount || 0,
            processedCount: processedCount || 0,
            currentStatus: '执行完成',
            message: '闲鱼脚本执行完成'
        });

        // 发送完成通知到服务器
        threads.start(function() {
            try {
                http.postJson("http://" + __serverHost + "/api/xianyu/execution-completed", {
                    taskId: __taskId,
                    deviceId: __deviceId,
                    success: true,
                    message: "闲鱼脚本执行完成",
                    timestamp: new Date().toISOString()
                }, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 5000
                });
                console.log("闲鱼执行完成状态上报成功");
            } catch (e) {
                console.log("闲鱼执行完成状态上报失败: " + e.message);
            }
        });

    } catch (error) {
        console.error("闲鱼脚本执行出错: " + error.message);

        // 发送执行失败状态到服务器
        sendXianyuRealtimeStatus({
            sentMessageCount: sentMessageCount || 0,
            processedCount: processedCount || 0,
            currentStatus: '执行失败',
            message: '闲鱼脚本执行失败: ' + error.message
        });

        // 发送失败通知到服务器
        threads.start(function() {
            try {
                http.postJson("http://" + __serverHost + "/api/xianyu/execution-completed", {
                    taskId: __taskId,
                    deviceId: __deviceId,
                    success: false,
                    message: "闲鱼脚本执行失败: " + error.message,
                    timestamp: new Date().toISOString()
                }, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 5000
                });
                console.log("闲鱼执行失败状态上报成功");
            } catch (e) {
                console.log("闲鱼执行失败状态上报失败: " + e.message);
            }
        });
    }
}, 1000);

// === 原始脚本开始 ===
`;

          // 移除原始脚本末尾的直接执行调用，并添加配置覆盖逻辑
          // 查找并移除脚本末尾的 startAutoChat() 调用
          let modifiedScriptContent = scriptContent;

          // 移除脚本末尾的直接执行调用
          modifiedScriptContent = modifiedScriptContent.replace(/\/\/ 直接开始执行\s*\n\s*startAutoChat\(\);\s*$/m, '');

          // 添加配置覆盖和执行逻辑
          const configOverrideAndExecution = `

// === 配置覆盖（在脚本执行前） ===
// 覆盖脚本中的默认配置
if (typeof config !== 'undefined' && __webConfig) {
    console.log("=== 开始覆盖配置 ===");
    console.log("原始配置:", JSON.stringify(config, null, 2));
    console.log("Web配置:", JSON.stringify(__webConfig, null, 2));

    // 更新config对象的属性
    if (__webConfig.targetCount !== undefined) config.targetCount = __webConfig.targetCount;
    if (__webConfig.keyword !== undefined) config.keyword = __webConfig.keyword;
    if (__webConfig.message !== undefined) config.message = __webConfig.message;
    if (__webConfig.searchKeyword !== undefined) config.keyword = __webConfig.searchKeyword;
    if (__webConfig.messageContent !== undefined) config.message = __webConfig.messageContent;
    if (__webConfig.operationDelay !== undefined) config.operationDelay = __webConfig.operationDelay;

    console.log("=== 配置覆盖完成 ===");
    console.log("最终配置:", JSON.stringify(config, null, 2));
    console.log("关键词:", config.keyword);
    console.log("私信内容:", config.message);
    console.log("目标数量:", config.targetCount);
} else {
    console.log("⚠️ 配置覆盖失败: config或__webConfig未定义");
    console.log("config类型:", typeof config);
    console.log("__webConfig类型:", typeof __webConfig);
}

// 开始执行脚本
console.log("🚀 开始执行闲鱼自动化脚本");
if (typeof startAutoChat === 'function') {
    startAutoChat();
} else {
    console.log("❌ startAutoChat函数未定义");
}
`;

          let finalScript = configInjection + modifiedScriptContent + configOverrideAndExecution;

          // 使用通用函数替换服务器地址
          finalScript = replaceServerAddresses(finalScript, req);

          // 记录到数据库（如果可用）- 添加用户ID
          if (xianyuLogService) {
            try {
              await xianyuLogService.createExecutionLogWithUserId(
                generatedTaskId,
                functionType,
                deviceId,
                device.deviceName || deviceId,
                deviceConfig,
                null, // scheduleConfig
                userId
              );
              console.log(`✅ 闲鱼执行日志已创建: ${generatedTaskId} (用户${userId})`);
            } catch (logError) {
              console.error('❌ 创建闲鱼执行日志失败:', logError);
            }
          }

          // 发送脚本到设备
          if (device.socketId.startsWith('http_')) {
            // HTTP设备：将命令存储到待执行队列
            if (!pendingCommands.has(deviceId)) {
              pendingCommands.set(deviceId, []);
            }
            pendingCommands.get(deviceId).push({
              logId: generatedTaskId,
              script: finalScript,
              timestamp: Date.now(),
              functionType: functionType,
              deviceConfig: deviceConfig
            });
            console.log(`📤 HTTP设备命令已排队: ${device.deviceName} (${deviceId})`);

            // HTTP设备也需要更新状态
            device.status = 'busy';
            device.lastActivity = new Date();
            console.log(`📱 HTTP设备状态已更新: ${deviceId} -> busy`);

            // 同时更新数据库状态
            await updateDeviceStatus(deviceId, 'busy');
          } else {
            // WebSocket设备：直接发送
            const deviceSocket = io.sockets.sockets.get(device.socketId);
            if (deviceSocket) {
              deviceSocket.emit('execute_script', {
                logId: generatedTaskId,
                script: finalScript,
                params: deviceConfig,
                timestamp: Date.now(),
                functionType: functionType
              });
              console.log(`📤 WebSocket设备命令已发送: ${device.deviceName} (${deviceId})`);

              // 更新设备状态为忙碌
              device.status = 'busy';
              device.lastActivity = new Date();
              console.log(`📱 设备状态已更新: ${deviceId} -> busy`);

              // 同时更新数据库状态
              await updateDeviceStatus(deviceId, 'busy');
            } else {
              throw new Error('设备连接已断开');
            }
          }

          // 广播初始状态到前端
          io.emit('xianyu_status_update', {
            deviceId: deviceId,
            taskId: generatedTaskId,
            status: 'starting',
            progress: 0,
            message: '脚本已发送，等待设备执行',
            stage: 'starting',
            timestamp: new Date().toISOString()
          });

          console.log(`📡 已广播闲鱼初始状态: ${deviceId} -> starting`);

          successCount++;
          executionResults.push({
            deviceId,
            deviceName: device.deviceName,
            taskId: generatedTaskId,
            status: 'queued',
            message: '任务已排队'
          });

        } catch (error) {
          console.error(`❌ 发送闲鱼任务到设备失败: ${deviceId}`, error);
          failCount++;
          executionResults.push({
            deviceId,
            deviceName: '未知设备',
            status: 'error',
            message: error.message
          });
        }
      }

      console.log(`📊 闲鱼任务执行统计: 成功=${successCount}, 失败=${failCount}, 总计=${deviceIds.length}`);

      res.json({
        success: true,
        message: `闲鱼任务已发送，成功: ${successCount}个，失败: ${failCount}个`,
        data: {
          functionType,
          successCount,
          failCount,
          totalCount: deviceIds.length,
          results: executionResults,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('❌ 执行闲鱼自动化任务失败:', error);
      res.status(500).json({
        success: false,
        message: '执行失败: ' + error.message
      });
    }
  });

  // 接收闲鱼脚本执行状态更新API (原始文件第4823行) - 已添加用户隔离
  app.post('/api/xianyu/status', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, status, progress, message, stage, taskId, debugInfo } = req.body;
      const userId = req.currentUserId;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      // 构造状态更新数据
      const statusData = {
        deviceId: deviceId,
        status: status,
        progress: progress,
        message: message,
        stage: stage,
        taskId: taskId,
        debugInfo: debugInfo,
        timestamp: new Date().toISOString()
      };

      // 更新闲鱼执行日志
      if (xianyuLogService && taskId) {
        try {
          // 首先检查任务是否已经被手动停止
          const currentLog = await xianyuLogService.getExecutionLogDetail(taskId);

          if (currentLog && currentLog.executionStatus === 'stopped') {
            console.log(`⚠️ 闲鱼任务 ${taskId} 已被手动停止，忽略状态更新: ${stage} - ${status}`);

            // 如果手机端发送的是停止状态，允许更新
            if (stage === 'stopped' || status === 'stopped') {
              console.log(`✅ 手机端确认停止状态，允许更新: ${taskId}`);
            } else {
              // 忽略其他状态更新，保持停止状态
              console.log(`🚫 忽略状态更新，保持停止状态: ${taskId}`);

              res.json({
                success: true,
                message: '任务已停止，忽略状态更新'
              });
              return;
            }
          }

          // 根据stage确定执行状态
          let executionStatus = 'running';
          if (stage === 'completed') {
            // 判断是否成功：status为'success'或者message中包含成功信息
            const isSuccess = status === 'success' ||
                             (message && (
                               message.includes('成功') ||
                               message.includes('完成') ||
                               message.includes('执行完成')
                             ));
            executionStatus = isSuccess ? 'completed' : 'failed';
          } else if (stage === 'error') {
            executionStatus = 'failed';
          } else if (stage === 'stopped' || status === 'stopped') {
            executionStatus = 'stopped';
          }

          await xianyuLogService.updateExecutionStatus(
            taskId,
            executionStatus,
            progress || 0,
            stage || '执行中',
            message
          );

          console.log(`闲鱼执行日志已更新: ${taskId} -> ${executionStatus} (${progress}%)`);
        } catch (logError) {
          console.error('更新闲鱼执行日志失败:', logError);
        }
      }

      // 通知所有Web客户端状态变化
      io.emit('xianyu_status_update', statusData);

      console.log(`📱 收到闲鱼状态更新: ${deviceId} - ${stage} - ${status} (${progress}%)`);
      if (message) {
        console.log(`📝 状态消息: ${message}`);
      }

      res.json({
        success: true,
        message: '状态更新已接收'
      });

    } catch (error) {
      console.error('❌ 处理闲鱼状态更新失败:', error);
      res.status(500).json({
        success: false,
        message: '处理状态更新失败: ' + error.message
      });
    }
  });

  // 接收闲鱼脚本实时状态上报API（设备端使用，不需要认证）
  app.post('/api/xianyu/realtime-status', (req, res) => {
    try {
      // 兼容多种数据格式
      const {
        deviceId,
        taskId,
        currentStatus,
        processedStepCount,
        searchAttemptCount,
        successCount,
        failedCount,
        // 兼容旧格式
        status,
        message,
        stage,
        data
      } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 构造实时状态数据（兼容新旧格式）
      const statusData = {
        deviceId,
        taskId: taskId || '',
        currentStatus: currentStatus || status || 'unknown',
        processedStepCount: processedStepCount || 0,
        searchAttemptCount: searchAttemptCount || 0,
        successCount: successCount || 0,
        failedCount: failedCount || 0,
        message: message || '',
        stage: stage || 'unknown',
        data: data || {},
        timestamp: new Date().toISOString()
      };

      // 通知所有Web客户端实时状态更新
      io.emit('xianyu_realtime_status', statusData);

      console.log(`📡 闲鱼实时状态: ${deviceId} - ${currentStatus || status} - 成功:${successCount || 0} 失败:${failedCount || 0}`);
      if (message) {
        console.log(`📝 实时消息: ${message}`);
      }

      res.json({
        success: true,
        message: '实时状态已接收'
      });

    } catch (error) {
      console.error('处理闲鱼实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 接收闲鱼脚本执行完成通知API (原始文件第9627行) - 已添加用户隔离
  app.post('/api/xianyu/execution-completed', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, taskId, success, message, results, timestamp } = req.body;
      const userId = req.currentUserId;

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`🏁 [用户${userId}] 闲鱼脚本执行完成: 设备=${deviceId}, 任务=${taskId}, 成功=${success}`);

      if (!deviceId || !taskId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 更新任务状态
      const task = xianyuActiveTasks.get(taskId);
      if (task) {
        task.status = success ? 'completed' : 'failed';
        task.completedAt = new Date();

        // 更新设备结果
        const deviceResult = task.results.find(r => r.deviceId === deviceId);
        if (deviceResult) {
          deviceResult.status = success ? 'completed' : 'failed';
          deviceResult.message = message || (success ? '执行成功' : '执行失败');
          deviceResult.results = results;
        }

        // 如果所有设备都完成了，将任务移到历史记录
        const allCompleted = task.results.every(r =>
          r.status === 'completed' || r.status === 'failed' || r.status === 'stopped'
        );

        if (allCompleted) {
          xianyuTaskHistory.push(task);
          xianyuActiveTasks.delete(taskId);
          console.log(`闲鱼任务 ${taskId} 已完成，移至历史记录`);
        }
      }

      // 通知前端执行完成
      io.emit('xianyu_execution_completed', {
        deviceId,
        taskId,
        success,
        message,
        results,
        timestamp: timestamp || new Date().toISOString()
      });

      res.json({
        success: true,
        message: '执行完成通知已接收'
      });

    } catch (error) {
      console.error('处理闲鱼执行完成通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 接收闲鱼脚本实时状态上报API (原始文件第9594行)
  app.post('/api/xianyu/realtime-status', (req, res) => {
    try {
      const { deviceId, status, message, stage, data } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 构造实时状态数据
      const statusData = {
        deviceId,
        status,
        message: message || '',
        stage: stage || 'unknown',
        data: data || {},
        timestamp: new Date().toISOString()
      };

      // 通知所有Web客户端实时状态更新
      io.emit('xianyu_realtime_status', statusData);

      console.log(`📡 闲鱼实时状态: ${deviceId} - ${stage} - ${status}`);
      if (message) {
        console.log(`📝 实时消息: ${message}`);
      }

      res.json({
        success: true,
        message: '实时状态已接收'
      });

    } catch (error) {
      console.error('处理闲鱼实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 接收闲鱼脚本执行完成通知API (原始文件第9627行)
  app.post('/api/xianyu/execution-completed', (req, res) => {
    try {
      const { deviceId, taskId, success, message, results, timestamp } = req.body;

      console.log(`🏁 闲鱼脚本执行完成: 设备=${deviceId}, 任务=${taskId}, 成功=${success}`);

      if (!deviceId || !taskId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 更新任务状态
      const task = xianyuActiveTasks.get(taskId);
      if (task) {
        task.status = success ? 'completed' : 'failed';
        task.completedAt = new Date();

        // 更新设备结果
        const deviceResult = task.results.find(r => r.deviceId === deviceId);
        if (deviceResult) {
          deviceResult.status = success ? 'completed' : 'failed';
          deviceResult.message = message || (success ? '执行成功' : '执行失败');
          deviceResult.results = results;
        }

        // 如果所有设备都完成了，将任务移到历史记录
        const allCompleted = task.results.every(r =>
          r.status === 'completed' || r.status === 'failed' || r.status === 'stopped'
        );

        if (allCompleted) {
          xianyuTaskHistory.push(task);
          xianyuActiveTasks.delete(taskId);
          console.log(`闲鱼任务 ${taskId} 已完成，移至历史记录`);
        }
      }

      // 通知前端执行完成
      io.emit('xianyu_execution_completed', {
        deviceId,
        taskId,
        success,
        message,
        results,
        timestamp: timestamp || new Date().toISOString()
      });

      res.json({
        success: true,
        message: '执行完成通知已接收'
      });

    } catch (error) {
      console.error('处理闲鱼执行完成通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // 清空闲鱼执行日志API (原始文件第9670行) - 已添加用户隔离
  app.delete('/api/xianyu/logs', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (xianyuLogService) {
        await xianyuLogService.clearExecutionLogs(userId);
        console.log('闲鱼执行日志已清空');
      } else {
        // 清空内存中的任务历史
        xianyuTaskHistory = [];
        xianyuActiveTasks.clear();
        console.log('内存中的闲鱼任务历史已清空');
      }

      res.json({
        success: true,
        message: '闲鱼执行日志已清空'
      });

    } catch (error) {
      console.error('清空闲鱼执行日志失败:', error);
      res.status(500).json({
        success: false,
        message: '清空失败: ' + error.message
      });
    }
  });

  // 终止所有闲鱼任务API (原始文件第9696行) - 已添加用户隔离
  app.post('/api/xianyu/stop-all', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { reason } = req.body;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 收到终止所有闲鱼任务请求，原因: ${reason}`);

      // 只停止当前用户的活跃任务
      const activeTasks = Array.from(xianyuActiveTasks.values()).filter(task =>
        task.userId === userId && task.status === 'running'
      );

      let stoppedCount = 0;
      const results = [];

      for (const task of activeTasks) {
        try {
          for (const deviceId of task.deviceIds) {
            // 查找设备
            let targetDevice = null;
            let targetSocket = null;

            for (const [socketId, deviceData] of devices) {
              if (deviceData.deviceId === deviceId) {
                targetDevice = deviceData;
                targetSocket = io.sockets.sockets.get(socketId);
                break;
              }
            }

            if (targetDevice && targetSocket) {
              // 发送停止命令
              targetSocket.emit('stop_script', {
                taskId: task.id,
                reason: reason || '管理员停止所有闲鱼任务'
              });

              console.log(`已向设备 ${deviceId} 发送停止命令，任务: ${task.id}`);

              // 延迟恢复设备状态
              setTimeout(() => {
                if (targetDevice) {
                  targetDevice.status = 'online';
                  targetDevice.lastActivity = new Date();
                  console.log(`批量停止：设备状态已恢复为在线: ${deviceId}`);

                  // 广播设备状态变化
                  io.emit('device_status_changed', {
                    type: 'device_status_updated',
                    deviceId: deviceId,
                    status: 'online',
                    message: '批量停止：闲鱼脚本已停止，设备恢复在线',
                    timestamp: new Date().toISOString()
                  });
                }
              }, 3000);
            }
          }

          // 更新任务状态
          task.status = 'stopped';
          task.completedAt = new Date();
          task.results.forEach(result => {
            if (result.status === 'sent' || result.status === 'running') {
              result.status = 'stopped';
              result.message = reason || '管理员停止所有闲鱼任务';
            }
          });

          stoppedCount++;
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            deviceCount: task.deviceIds.length,
            status: 'stopped'
          });

        } catch (error) {
          console.error(`停止闲鱼任务失败: ${task.id}`, error);
          results.push({
            taskId: task.id,
            functionType: task.functionType,
            deviceCount: task.deviceIds.length,
            status: 'error',
            message: error.message
          });
        }
      }

      res.json({
        success: true,
        message: `批量停止完成，成功停止 ${stoppedCount} 个闲鱼任务`,
        data: {
          stoppedCount,
          totalTasks: activeTasks.length,
          results
        }
      });

    } catch (error) {
      console.error('停止所有闲鱼任务失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 停止闲鱼自动化任务API (原始文件第10111行) - 已添加用户隔离
  app.post('/api/xianyu/stop', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, taskId, logId } = req.body;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 收到停止闲鱼自动化任务请求:`, { deviceId, taskId, logId });

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      // 如果传递了logId但没有taskId，将logId作为taskId使用
      let actualTaskId = taskId;
      if (!actualTaskId && logId) {
        actualTaskId = logId;
        console.log(`使用logId作为taskId: ${actualTaskId}`);
      }

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID'
        });
      }

      // 查找设备连接方式并发送停止命令
      let deviceSocket = null;
      let deviceData = null;

      // 查找设备数据和socket
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          deviceData = device;
          if (!socketId.startsWith('http_')) {
            deviceSocket = io.sockets.sockets.get(socketId);
          }
          break;
        }
      }

      if (deviceSocket) {
        // WebSocket连接的设备
        console.log(`向WebSocket设备发送停止命令: ${deviceId}`);
        deviceSocket.emit('script_command', {
          type: 'stop_script',
          taskId: actualTaskId
        });

        // 延迟恢复设备状态（给设备时间处理停止命令）
        setTimeout(() => {
          if (deviceData) {
            deviceData.status = 'online';
            deviceData.lastActivity = new Date();
            console.log(`WebSocket设备状态已恢复为在线: ${deviceId}`);

            // 广播设备状态变化
            io.emit('device_status_changed', {
              type: 'device_status_updated',
              deviceId: deviceId,
              status: 'online',
              message: '闲鱼脚本已停止，设备恢复在线',
              timestamp: new Date().toISOString()
            });
          }
        }, 3000);

      } else if (deviceData) {
        // HTTP连接的设备，添加停止命令到队列
        console.log(`设备 ${deviceId} 通过HTTP连接，添加停止命令到队列`);

        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        // 添加停止命令到队列
        pendingCommands.get(deviceId).push({
          script: 'STOP_SCRIPT_COMMAND',
          type: 'stop_script',
          taskId: actualTaskId,
          timestamp: new Date()
        });

        console.log(`已将停止命令添加到设备 ${deviceId} 的队列`);

        // 延迟恢复HTTP设备状态
        setTimeout(() => {
          deviceData.status = 'online';
          deviceData.lastActivity = new Date();
          console.log(`HTTP设备状态已恢复为在线: ${deviceId}`);

          // 广播设备状态变化
          io.emit('device_status_changed', {
            type: 'device_status_updated',
            deviceId: deviceId,
            status: 'online',
            message: '闲鱼脚本已停止，设备恢复在线',
            timestamp: new Date().toISOString()
          });
        }, 5000);

      } else {
        console.log(`设备 ${deviceId} 未连接，无法发送停止命令`);
      }

      // 更新任务状态
      if (actualTaskId && xianyuActiveTasks.has(actualTaskId)) {
        const task = xianyuActiveTasks.get(actualTaskId);
        task.status = 'stopped';
        task.endTime = new Date();
        xianyuActiveTasks.delete(actualTaskId);
        xianyuTaskHistory.push(task);
      }

      // 更新数据库中的执行日志状态
      if (xianyuLogService) {
        try {
          if (actualTaskId) {
            // 有明确的taskId，直接更新
            console.log(`更新数据库执行日志状态: ${actualTaskId} -> stopped`);
            await xianyuLogService.updateExecutionStatus(
              actualTaskId,
              'stopped',  // 执行状态改为已停止
              0,          // 进度重置为0
              '已停止',   // 阶段描述
              '用户手动停止任务'  // 消息
            );
            console.log(`✅ 数据库执行日志已更新: ${actualTaskId} -> stopped (0%)`);
          } else {
            // 没有taskId，尝试查找该设备正在执行的任务
            console.log(`没有taskId，查找设备 ${deviceId} 正在执行的任务`);

            // 查找该设备最近的执行中任务
            const runningLogs = await xianyuLogService.getExecutionLogs(
              1, // page
              10, // limit
              {
                executionStatus: 'running',
                deviceId: deviceId,
                userId: userId
              }
            );

            if (runningLogs.logs && runningLogs.logs.length > 0) {
              const latestRunningLog = runningLogs.logs[0];
              console.log(`找到正在执行的任务: ${latestRunningLog.logId}`);

              await xianyuLogService.updateExecutionStatus(
                latestRunningLog.logId,
                'stopped',
                0,
                '已停止',
                '用户手动停止任务（无taskId）'
              );
              console.log(`✅ 数据库执行日志已更新: ${latestRunningLog.logId} -> stopped (0%)`);
            } else {
              console.log(`未找到设备 ${deviceId} 正在执行的任务`);
            }
          }
        } catch (dbError) {
          console.error('更新数据库执行日志失败:', dbError);
        }
      }

      res.json({
        success: true,
        message: '停止命令已发送',
        data: {
          deviceId,
          taskId: actualTaskId,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('停止闲鱼自动化任务失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 测试路由API (原始文件第10250行)
  app.get('/api/xianyu/test', (req, res) => {
    res.json({
      success: true,
      message: '闲鱼自动化API正常工作',
      timestamp: new Date().toISOString(),
      routes: [
        'GET /api/xianyu/test',
        'POST /api/xianyu/execute',
        'POST /api/xianyu/stop',
        'GET /api/xianyu/logs',
        'POST /api/xianyu/chat-record',
        'GET /api/xianyu/chat-records/:deviceId',
        'DELETE /api/xianyu/chat-records/:deviceId/all'
      ]
    });
  });



  // 检查设备执行状态API (原始文件第10395行) - 已添加用户隔离
  app.get('/api/xianyu/device-status/:deviceId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId } = req.params;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 查询设备执行状态: ${deviceId}`);

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权查看此设备状态'
        });
      }

      // 查找设备的活跃任务（只查找当前用户的任务）
      const deviceTasks = Array.from(xianyuActiveTasks.values()).filter(task =>
        task.userId === userId && task.deviceIds.includes(deviceId)
      );

      // 查找设备连接状态
      let deviceConnected = false;
      let deviceInfo = null;

      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          deviceConnected = true;
          deviceInfo = {
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            status: device.status,
            connectedAt: device.connectedAt,
            lastSeen: device.lastSeen
          };
          break;
        }
      }

      // 查询数据库中的执行状态
      let databaseStatus = null;
      if (xianyuLogService) {
        try {
          const runningLogs = await xianyuLogService.getExecutionLogs(
            1, // page
            1, // limit
            {
              executionStatus: 'running',
              deviceId: deviceId,
              userId: userId
            }
          );

          if (runningLogs.logs && runningLogs.logs.length > 0) {
            databaseStatus = runningLogs.logs[0];
          }
        } catch (dbError) {
          console.error('查询数据库执行状态失败:', dbError);
        }
      }

      res.json({
        success: true,
        data: {
          deviceId,
          connected: deviceConnected,
          deviceInfo,
          activeTasks: deviceTasks.map(task => ({
            id: task.id,
            functionType: task.functionType,
            status: task.status,
            createdAt: task.createdAt,
            startedAt: task.startedAt
          })),
          databaseStatus,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('查询设备执行状态失败:', error);
      res.status(500).json({
        success: false,
        message: '查询失败: ' + error.message
      });
    }
  });

  // 用户隔离中间件和工具已在文件开头引入

  // 获取闲鱼执行日志API (原始文件第10465行) - 已添加用户隔离
  app.get('/api/xianyu/logs', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { page = 1, limit = 20, functionType, status, executionStatus, deviceId } = req.query;
      const userId = req.currentUserId;

      // 支持两种参数名：status 和 executionStatus
      const finalExecutionStatus = executionStatus || status;

      console.log(`[闲鱼日志] 用户${userId}查询执行日志: page=${page}, limit=${limit}, functionType=${functionType}, executionStatus=${finalExecutionStatus}, deviceId=${deviceId}`);

      if (xianyuLogService) {
        const logs = await xianyuLogService.getExecutionLogsWithUserFilter(
          parseInt(page),
          parseInt(limit),
          {
            functionType,
            executionStatus: finalExecutionStatus,
            deviceId,
            userId  // 添加用户ID过滤
          }
        );

        console.log(`[闲鱼日志] 用户${userId}查询到${logs.data ? logs.data.length : 0}条日志`);

        res.json({
          success: true,
          data: logs
        });
      } else {
        // 如果日志服务不可用，返回内存中的任务历史（需要按用户过滤）
        const offset = (page - 1) * limit;
        let filteredTasks = xianyuTaskHistory.filter(task => task.userId === userId);

        if (functionType) {
          filteredTasks = filteredTasks.filter(task => task.functionType === functionType);
        }
        if (status) {
          filteredTasks = filteredTasks.filter(task => task.status === status);
        }
        if (deviceId) {
          filteredTasks = filteredTasks.filter(task => task.deviceIds.includes(deviceId));
        }

        const paginatedTasks = filteredTasks.slice(offset, offset + parseInt(limit));

        res.json({
          success: true,
          data: {
            logs: paginatedTasks.map(task => ({
              id: task.id,
              functionType: task.functionType,
              executionStatus: task.status,
              deviceId: task.deviceIds[0] || null,
              deviceName: task.deviceName || '未知设备',
              startedAt: task.startedAt,
              completedAt: task.completedAt,
              progressPercentage: task.status === 'completed' ? 100 : 0,
              statusMessage: task.status,
              configData: JSON.stringify(task.config)
            })),
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: filteredTasks.length,
              totalPages: Math.ceil(filteredTasks.length / limit)
            }
          }
        });
      }

    } catch (error) {
      console.error('获取闲鱼执行日志失败:', error);
      res.status(500).json({
        success: false,
        message: '获取执行日志失败: ' + error.message
      });
    }
  });

  // 创建私聊记录（由手机端脚本调用）API (原始文件第10850行) - 已添加用户隔离
  app.post('/api/xianyu/chat-record', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, keyword, username, message, success, timestamp } = req.body;
      const userId = req.currentUserId;

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`[用户${userId}] 收到闲鱼私聊记录:`, {
        deviceId,
        keyword,
        username,
        message,
        success,
        timestamp
      });

      if (!deviceId || !keyword || !username) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 如果有闲鱼私聊服务，保存到数据库
      if (pool) {
        try {
          const [result] = await pool.execute(`
            INSERT INTO xianyu_chat_records (device_id, device_name, keyword, seller_name, message_content, chat_time)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [deviceId, deviceId, keyword, username, message || '', timestamp || new Date()]);

          console.log('闲鱼私聊记录已保存到数据库:', result.insertId);

          res.json({
            success: true,
            message: '私聊记录已保存',
            data: {
              recordId: result.insertId
            }
          });
        } catch (dbError) {
          console.error('数据库保存私聊记录失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库保存失败: ' + dbError.message
          });
        }
      } else {
        // 数据库不可用，保存到内存
        const recordId = Date.now().toString() + Math.random();
        chatRecords.set(recordId, {
          id: recordId,
          deviceId,
          keyword,
          username,
          message: message || '',
          success: success ? 1 : 0,
          chatTime: timestamp || new Date(),
          createdAt: new Date()
        });

        console.log('闲鱼私聊记录已保存到内存:', recordId);

        res.json({
          success: true,
          message: '私聊记录已保存（内存存储）',
          data: {
            recordId
          }
        });
      }

    } catch (error) {
      console.error('保存私聊记录失败:', error);
      res.status(500).json({
        success: false,
        message: '保存失败: ' + error.message
      });
    }
  });

  // 获取设备的私聊记录列表API (原始文件第10929行) - 已添加用户隔离
  app.get('/api/xianyu/chat-records/:deviceId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId } = req.params;
      const { page = 1, limit = 50, keyword = '' } = req.query;
      const userId = req.currentUserId;

      console.log(`[用户${userId}] 获取私聊记录:`, { deviceId, page, limit, keyword });

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权查看此设备的私聊记录'
        });
      }

      if (pool) {
        try {
          let whereClause = 'WHERE device_id = ?';
          let params = [deviceId];

          if (keyword) {
            whereClause += ' AND (keyword LIKE ? OR seller_name LIKE ? OR message_content LIKE ?)';
            const searchTerm = `%${keyword}%`;
            params.push(searchTerm, searchTerm, searchTerm);
          }

          const offset = (page - 1) * limit;

          const [records] = await pool.execute(`
            SELECT id, device_id, keyword, seller_name as username, message_content as message, chat_time
            FROM xianyu_chat_records
            ${whereClause}
            ORDER BY chat_time DESC
            LIMIT ? OFFSET ?
          `, [...params, parseInt(limit), offset]);

          const [countResult] = await pool.execute(`
            SELECT COUNT(*) as total
            FROM xianyu_chat_records
            ${whereClause}
          `, params);

          const total = countResult[0].total;

          res.json({
            success: true,
            data: {
              records,
              total,
              page: parseInt(page),
              limit: parseInt(limit),
              totalPages: Math.ceil(total / limit)
            }
          });
        } catch (dbError) {
          console.error('数据库查询私聊记录失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库查询失败: ' + dbError.message
          });
        }
      } else {
        // 数据库不可用，从内存中查询
        let records = Array.from(chatRecords.values()).filter(record =>
          record.deviceId === deviceId
        );

        if (keyword) {
          records = records.filter(record =>
            record.keyword.includes(keyword) ||
            record.username.includes(keyword) ||
            record.message.includes(keyword)
          );
        }

        records.sort((a, b) => new Date(b.chatTime) - new Date(a.chatTime));

        const offset = (page - 1) * limit;
        const paginatedRecords = records.slice(offset, offset + parseInt(limit));

        res.json({
          success: true,
          data: {
            records: paginatedRecords,
            total: records.length,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(records.length / limit)
          }
        });
      }

    } catch (error) {
      console.error('获取私聊记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取失败: ' + error.message
      });
    }
  });

  // 获取设备私聊统计信息API (原始文件第10980行) - 已添加用户隔离
  app.get('/api/xianyu/chat-statistics/:deviceId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId } = req.params;
      const userId = req.currentUserId;

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权查看此设备的统计信息'
        });
      }

      if (pool) {
        try {
          // 获取基本统计信息
          const [basicStats] = await pool.execute(`
            SELECT
              COUNT(*) as totalChats,
              0 as successfulChats,
              0 as failedChats,
              COUNT(DISTINCT keyword) as uniqueKeywords,
              COUNT(DISTINCT seller_name) as uniqueUsers
            FROM xianyu_chat_records
            WHERE device_id = ?
          `, [deviceId]);

          // 获取关键词统计
          const [keywordStats] = await pool.execute(`
            SELECT
              keyword,
              COUNT(*) as count,
              COUNT(*) as successCount,
              0 as failCount
            FROM xianyu_chat_records
            WHERE device_id = ? AND user_id = ?
            GROUP BY keyword
            ORDER BY count DESC
            LIMIT 10
          `, [deviceId, userId]);

          // 获取最近7天的统计
          const [recentStats] = await pool.execute(`
            SELECT
              DATE(chat_time) as date,
              COUNT(*) as count,
              COUNT(*) as successCount
            FROM xianyu_chat_records
            WHERE device_id = ? AND user_id = ? AND chat_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(chat_time)
            ORDER BY date DESC
          `, [deviceId, userId]);

          res.json({
            success: true,
            data: {
              statistics: basicStats[0],
              keywordStats,
              recentStats
            }
          });
        } catch (dbError) {
          console.error('数据库获取私聊统计失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库查询失败: ' + dbError.message
          });
        }
      } else {
        // 数据库不可用，从内存中统计
        const deviceRecords = Array.from(chatRecords.values()).filter(record =>
          record.deviceId === deviceId
        );

        const totalChats = deviceRecords.length;
        const successfulChats = totalChats; // 假设所有记录都是成功的
        const failedChats = 0;
        const uniqueKeywords = new Set(deviceRecords.map(r => r.keyword)).size;
        const uniqueUsers = new Set(deviceRecords.map(r => r.username)).size;

        // 关键词统计
        const keywordMap = new Map();
        deviceRecords.forEach(record => {
          const keyword = record.keyword;
          if (!keywordMap.has(keyword)) {
            keywordMap.set(keyword, { count: 0, successCount: 0, failCount: 0 });
          }
          const stats = keywordMap.get(keyword);
          stats.count++;
          stats.successCount++; // 假设所有记录都是成功的
        });

        const keywordStats = Array.from(keywordMap.entries()).map(([keyword, stats]) => ({
          keyword,
          ...stats
        })).sort((a, b) => b.count - a.count).slice(0, 10);

        res.json({
          success: true,
          data: {
            statistics: {
              totalChats,
              successfulChats,
              failedChats,
              uniqueKeywords,
              uniqueUsers
            },
            keywordStats,
            recentStats: []
          }
        });
      }

    } catch (error) {
      console.error('获取私聊统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取统计失败: ' + error.message
      });
    }
  });

  // 删除单条私聊记录API (原始文件第11050行) - 已添加用户隔离
  app.delete('/api/xianyu/chat-record/:recordId', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { recordId } = req.params;
      const userId = req.currentUserId;

      if (pool) {
        try {
          const [result] = await pool.execute(`
            DELETE FROM xianyu_chat_records WHERE id = ? AND user_id = ?
          `, [recordId, userId]);

          if (result.affectedRows === 0) {
            return res.status(404).json({
              success: false,
              message: '私聊记录不存在'
            });
          }

          console.log(`私聊记录已删除: ${recordId}`);

          res.json({
            success: true,
            message: '私聊记录删除成功'
          });
        } catch (dbError) {
          console.error('数据库删除私聊记录失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库删除失败: ' + dbError.message
          });
        }
      } else {
        // 从内存中删除
        if (!chatRecords.has(recordId)) {
          return res.status(404).json({
            success: false,
            message: '私聊记录不存在'
          });
        }

        chatRecords.delete(recordId);
        console.log(`私聊记录已从内存删除: ${recordId}`);

        res.json({
          success: true,
          message: '私聊记录删除成功（内存存储）'
        });
      }

    } catch (error) {
      console.error('删除私聊记录失败:', error);
      res.status(500).json({
        success: false,
        message: '删除失败: ' + error.message
      });
    }
  });

  // 批量删除私聊记录API (原始文件第11100行) - 已添加用户隔离
  app.delete('/api/xianyu/chat-records/batch', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { recordIds } = req.body;
      const userId = req.currentUserId;

      if (!recordIds || !Array.isArray(recordIds) || recordIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: '缺少记录ID列表'
        });
      }

      if (pool) {
        try {
          const placeholders = recordIds.map(() => '?').join(',');
          const [result] = await pool.execute(`
            DELETE FROM xianyu_chat_records WHERE id IN (${placeholders}) AND user_id = ?
          `, [...recordIds, userId]);

          console.log(`批量删除私聊记录: ${result.affectedRows} 条`);

          res.json({
            success: true,
            message: `成功删除 ${result.affectedRows} 条私聊记录`
          });
        } catch (dbError) {
          console.error('数据库批量删除私聊记录失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库删除失败: ' + dbError.message
          });
        }
      } else {
        // 从内存中批量删除
        let deletedCount = 0;
        recordIds.forEach(recordId => {
          if (chatRecords.has(recordId)) {
            chatRecords.delete(recordId);
            deletedCount++;
          }
        });

        console.log(`批量删除私聊记录（内存）: ${deletedCount} 条`);

        res.json({
          success: true,
          message: `成功删除 ${deletedCount} 条私聊记录（内存存储）`
        });
      }

    } catch (error) {
      console.error('批量删除私聊记录失败:', error);
      res.status(500).json({
        success: false,
        message: '批量删除失败: ' + error.message
      });
    }
  });

  // 清空设备的所有私聊记录API (原始文件第11150行) - 已添加用户隔离
  app.delete('/api/xianyu/chat-records/:deviceId/all', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId } = req.params;
      const userId = req.currentUserId;

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备的私聊记录'
        });
      }

      if (pool) {
        try {
          const [result] = await pool.execute(`
            DELETE FROM xianyu_chat_records WHERE device_id = ? AND user_id = ?
          `, [deviceId, userId]);

          console.log(`[用户${userId}] 清空设备私聊记录: ${deviceId}, 删除 ${result.affectedRows} 条`);

          res.json({
            success: true,
            message: `成功清空设备 ${deviceId} 的 ${result.affectedRows} 条私聊记录`
          });
        } catch (dbError) {
          console.error('数据库清空私聊记录失败:', dbError);
          res.status(500).json({
            success: false,
            message: '数据库清空失败: ' + dbError.message
          });
        }
      } else {
        // 从内存中清空设备记录
        let deletedCount = 0;
        for (const [recordId, record] of chatRecords) {
          if (record.deviceId === deviceId) {
            chatRecords.delete(recordId);
            deletedCount++;
          }
        }

        console.log(`清空设备私聊记录（内存）: ${deviceId}, 删除 ${deletedCount} 条`);

        res.json({
          success: true,
          message: `成功清空设备 ${deviceId} 的 ${deletedCount} 条私聊记录（内存存储）`
        });
      }

    } catch (error) {
      console.error('清空设备私聊记录失败:', error);
      res.status(500).json({
        success: false,
        message: '清空失败: ' + error.message
      });
    }
  });

  // 闲鱼执行日志停止按钮API - 已添加用户隔离
  app.post('/api/xianyu/logs/:logId/stop', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { logId } = req.params;
      const userId = req.currentUserId;

      if (!logId) {
        return res.status(400).json({
          success: false,
          message: '缺少日志ID参数'
        });
      }

      console.log(`[用户${userId}] 收到闲鱼执行日志停止请求: logId=${logId}`);

      // 获取执行日志详情
      if (!xianyuLogService) {
        return res.status(500).json({
          success: false,
          message: '日志服务不可用'
        });
      }

      const logDetail = await xianyuLogService.getExecutionLogDetail(logId);
      if (!logDetail) {
        return res.status(404).json({
          success: false,
          message: '执行日志不存在'
        });
      }

      const deviceId = logDetail.device_id;
      const taskId = logDetail.task_id;

      console.log(`闲鱼执行日志停止: deviceId=${deviceId}, taskId=${taskId}`);

      // 调用停止脚本API的逻辑
      const stopResult = await stopXianyuScriptExecution(deviceId, taskId, logId);

      if (stopResult.success) {
        res.json({
          success: true,
          message: '停止命令已发送'
        });
      } else {
        res.status(500).json({
          success: false,
          message: stopResult.message
        });
      }

    } catch (error) {
      console.error('闲鱼执行日志停止失败:', error);
      res.status(500).json({
        success: false,
        message: '停止失败: ' + error.message
      });
    }
  });

  // 停止闲鱼脚本执行的内部函数
  async function stopXianyuScriptExecution(deviceId, taskId, logId) {
    try {
      // 查找设备
      let deviceSocket = null;
      let deviceData = null;

      // 查找设备数据
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          deviceData = device;
          if (!socketId.startsWith('http_')) {
            deviceSocket = io.sockets.sockets.get(socketId);
          }
          break;
        }
      }

      if (deviceSocket) {
        // WebSocket连接的设备
        console.log(`向WebSocket设备发送停止命令: ${deviceId}`);
        deviceSocket.emit('script_command', {
          type: 'stop_script',
          taskId: taskId
        });

        // 更新执行状态为停止中
        if (xianyuLogService && logId) {
          await xianyuLogService.updateExecutionStatus(
            logId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行'
          );
        }

        return { success: true, message: '停止命令已发送到WebSocket设备' };
      } else if (deviceData) {
        // HTTP连接的设备，添加停止命令到队列
        console.log(`HTTP设备，添加停止命令到队列: ${deviceId}`);

        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        pendingCommands.get(deviceId).push({
          script: 'STOP_SCRIPT_COMMAND',
          type: 'stop_script',
          taskId: taskId,
          timestamp: new Date()
        });

        // 更新执行状态为停止中
        if (xianyuLogService && logId) {
          await xianyuLogService.updateExecutionStatus(
            logId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行（HTTP设备）'
          );
        }

        return { success: true, message: '停止命令已添加到队列' };
      } else {
        return { success: false, message: '设备未连接，无法发送停止命令' };
      }

    } catch (error) {
      console.error('停止闲鱼脚本执行失败:', error);
      return { success: false, message: '停止失败: ' + error.message };
    }
  }

  console.log('✅ 闲鱼自动化模块设置完成');

  // 返回闲鱼自动化相关函数供其他模块使用
  return {
    xianyuActiveTasks,
    xianyuTaskHistory,
    chatRecords
  };
}

module.exports = { setupServerXianyu };
