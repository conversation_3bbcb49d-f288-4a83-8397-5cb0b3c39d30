(function(){var e={3019:function(e,t,n){"use strict";var s=n(5471),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},i=[],c={name:"App"},a=c,r=n(1656),l=(0,r.A)(a,o,i,!1,null,null,null),u=l.exports,d=n(173),g=n(4310);s["default"].use(d.Ay);const h=[{path:"/login",name:"Login",component:()=>n.e(145).then(n.bind(n,5145)),meta:{requiresAuth:!1}},{path:"/",name:"Layout",component:()=>n.e(400).then(n.bind(n,4400)),meta:{requiresAuth:!0},redirect:"/dashboard",children:[{path:"dashboard",name:"Dashboard",component:()=>n.e(547).then(n.bind(n,547)),meta:{title:"仪表盘"}},{path:"devices",name:"Devices",component:()=>n.e(942).then(n.bind(n,4942)),meta:{title:"设备管理"}},{path:"scripts",name:"Scripts",component:()=>n.e(838).then(n.bind(n,3838)),meta:{title:"脚本管理"}},{path:"script-config",name:"ScriptConfig",component:()=>n.e(928).then(n.bind(n,4928)),meta:{title:"脚本配置执行"}},{path:"files",name:"Files",component:()=>n.e(578).then(n.bind(n,578)),meta:{title:"文件管理"}},{path:"logs",name:"Logs",component:()=>n.e(384).then(n.bind(n,9384)),meta:{title:"执行日志"}},{path:"xiaohongshu",name:"XiaohongshuAutomation",component:()=>Promise.all([n.e(534),n.e(205)]).then(n.bind(n,27)),meta:{title:"小红书自动化"}},{path:"xiaohongshu-logs",name:"XiaohongshuLogs",component:()=>n.e(688).then(n.bind(n,4688)),meta:{title:"小红书执行日志"}},{path:"xianyu",name:"XianyuAutomation",component:()=>Promise.all([n.e(534),n.e(431)]).then(n.bind(n,6810)),meta:{title:"闲鱼自动化"}},{path:"xianyu-logs",name:"XianyuLogs",component:()=>n.e(258).then(n.bind(n,8258)),meta:{title:"闲鱼执行日志"}},{path:"xianyu-chat-records/:deviceId",name:"XianyuChatRecords",component:()=>n.e(483).then(n.bind(n,4483)),meta:{title:"闲鱼私聊记录"}},{path:"admin/activation-codes",name:"AdminActivationCodes",component:()=>n.e(414).then(n.bind(n,1414)),meta:{title:"卡密管理",requiresAdmin:!0}},{path:"admin/users",name:"AdminUserManagement",component:()=>n.e(511).then(n.bind(n,2511)),meta:{title:"用户管理",requiresAdmin:!0}}]}],m=new d.Ay({mode:"history",base:"/",routes:h});m.beforeEach((e,t,n)=>{const s=g.A.getters["auth/isAuthenticated"],o=g.A.getters["auth/user"];e.matched.some(e=>!1!==e.meta.requiresAuth)?s?e.matched.some(e=>e.meta.requiresAdmin)?o&&"admin"===o.role?n():n("/dashboard"):n():n("/login"):"/login"===e.path&&s?n("/"):n()});var p=m,S=n(1052),T=n.n(S),f=n(4335),E=n(5093),v=n.n(E),k=(n(5596),n(9381)),_=n(6006);s["default"].config.productionTip=!1,s["default"].use(T());const C=()=>{const e=(0,k.getApiBaseUrl)();f.A.defaults.baseURL=e,f.A.defaults.timeout=2e6,console.log("Axios baseURL configured to:",e),console.log("Current location:",{hostname:window.location.hostname,port:window.location.port,protocol:window.location.protocol})};C(),f.A.interceptors.request.use(e=>{const t=g.A.getters["auth/token"];return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),f.A.interceptors.response.use(e=>e,e=>{if(e.response){const{status:t,data:n}=e.response;401===t?(g.A.dispatch("auth/logout"),p.push("/login"),s["default"].prototype.$message.error("登录已过期，请重新登录")):s["default"].prototype.$message.error(n.message||"请求失败")}else s["default"].prototype.$message.error("网络错误");return Promise.reject(e)}),s["default"].prototype.$http=f.A,v().locale("zh-cn"),s["default"].prototype.$moment=v(),s["default"].filter("formatTime",function(e){return e?v()(e).format("YYYY-MM-DD HH:mm:ss"):""}),s["default"].filter("fromNow",function(e){return e?v()(e).fromNow():""});new s["default"]({router:p,store:g.A,render:e=>e(u)}).$mount("#app");function I(e){try{console.log("🔧 [Main] 设置路由监听器"),p.beforeEach((t,n,s)=>{t.path!==n.path&&(console.log("🔄 [Main] 检测到路由切换:",n.path,"->",t.path),e.isRouteChanging=!0,e.socket&&e.isConnected&&e.socket.emit("route_changing",{from:n.path,to:t.path,timestamp:(new Date).toISOString()})),s()}),p.afterEach((t,n)=>{setTimeout(()=>{console.log("🔄 [Main] 路由切换完成，重置标识"),e.isRouteChanging=!1,!e.isInitialized||e.isConnected&&e.socket&&!e.socket.disconnected||(console.log("🔄 [Main] 路由切换后检测到连接断开，重新连接"),setTimeout(()=>{e.init()},1e3))},500)}),console.log("✅ [Main] 路由监听器设置完成")}catch(t){console.error("❌ [Main] 设置路由监听器失败:",t)}}setTimeout(async()=>{try{console.log("🔧 [Main] 应用启动完成，初始化WebSocket连接..."),await(0,_.MC)();const{getWebSocketManager:e}=await Promise.resolve().then(n.bind(n,6006)),t=e();g.A.commit("socket/SET_WEBSOCKET_MANAGER",t),I(t),console.log("✅ [Main] WebSocket连接初始化完成，管理器已设置到store")}catch(e){console.error("❌ [Main] WebSocket连接初始化失败:",e)}},1e3),g.A.watch(e=>e.auth.token,async e=>{console.log("🔧 [Main] Token变化，重新初始化WebSocket连接:",!!e);try{const{getWebSocketManager:e}=await Promise.resolve().then(n.bind(n,6006)),t=e();t.disconnect(),setTimeout(async()=>{try{await t.init(),g.A.commit("socket/SET_WEBSOCKET_MANAGER",t),console.log("✅ [Main] WebSocket重连成功，管理器已更新到store")}catch(e){console.error("❌ [Main] WebSocket重连失败:",e)}},500)}catch(t){console.error("❌ [Main] WebSocket重连过程出错:",t)}})},4310:function(e,t,n){"use strict";n.d(t,{A:function(){return K}});var s=n(5471),o=n(5353),i=n(4335),c=n(8987);const a={token:c.A.get("token")||"",user:JSON.parse(localStorage.getItem("user")||"{}")},r={token:e=>e.token,user:e=>e.user,isAuthenticated:e=>!!e.token},l={SET_TOKEN(e,t){e.token=t,t?c.A.set("token",t,{expires:1}):c.A.remove("token")},SET_USER(e,t){e.user=t,t&&Object.keys(t).length>0?localStorage.setItem("user",JSON.stringify(t)):localStorage.removeItem("user")}},u={async login({commit:e},{username:t,password:n}){try{const s=await i.A.post("/api/auth/login",{username:t,password:n});if(s.data.success&&s.data.token)return e("SET_TOKEN",s.data.token),e("SET_USER",s.data.user),{success:!0,message:s.data.message};throw new Error(s.data.message||"登录失败")}catch(s){throw s}},async register({commit:e},{username:t,password:n,email:s}){try{const e=await i.A.post("/api/auth/register",{username:t,password:n,email:s});return{success:!0,message:e.data.message}}catch(o){throw o}},async verifyToken({commit:e,state:t}){if(!t.token)return!1;try{const t=await i.A.get("/api/auth/verify");return!(!t.data.success||!t.data.user)&&(e("SET_USER",t.data.user),!0)}catch(n){return e("SET_TOKEN",""),e("SET_USER",{}),!1}},logout({commit:e}){e("SET_TOKEN",""),e("SET_USER",{})}};var d={namespaced:!0,state:a,getters:r,mutations:l,actions:u};const g={devices:[],selectedDevices:[],loading:!1,deviceExecutionStatus:{}},h={devices:e=>e.devices,selectedDevices:e=>e.selectedDevices,onlineDevices:e=>e.devices.filter(e=>"online"===e.status),offlineDevices:e=>e.devices.filter(e=>"offline"===e.status),busyDevices:e=>e.devices.filter(e=>"busy"===e.status),loading:e=>e.loading,deviceExecutionStatus:e=>e.deviceExecutionStatus,getDeviceExecutionStatus:e=>t=>e.deviceExecutionStatus[t]||null,isDeviceExecutingFunction:e=>(t,n)=>{const s=e.deviceExecutionStatus[t];return s&&s.functionType===n},isDeviceExecutingAnyFunction:e=>t=>!!e.deviceExecutionStatus[t],getDevicesExecutingModule:e=>t=>{const n=[];return Object.keys(e.deviceExecutionStatus).forEach(s=>{const o=e.deviceExecutionStatus[s];o&&o.module===t&&n.push({deviceId:s,functionType:o.functionType,startTime:o.startTime})}),n}},m={SET_DEVICES(e,t){e.devices=t},ADD_DEVICE(e,t){const n=e.devices.findIndex(e=>e.device_id===t.device_id);n>=0?e.devices.splice(n,1,t):e.devices.push(t)},UPDATE_DEVICE(e,{deviceId:t,updates:n}){const s=e.devices.find(e=>e.device_id===t);s?(Object.assign(s,n),console.log(`Store中设备 ${t} 状态已更新:`,n)):console.warn(`Store中未找到设备 ${t}，无法更新状态`)},UPDATE_DEVICE_STATUS(e,{deviceId:t,status:n,lastSeen:s}){const o=e.devices.find(e=>e.device_id===t);o?(o.status=n,s&&(o.last_seen=s),console.log(`[Device Store] 设备状态已更新: ${t} -> ${n}`)):console.log(`[Device Store] 设备未找到，无法更新状态: ${t}`)},REMOVE_DEVICE(e,t){const n=e.devices.findIndex(e=>e.device_id===t);n>=0&&e.devices.splice(n,1)},SET_SELECTED_DEVICES(e,t){e.selectedDevices=t},SET_LOADING(e,t){e.loading=t},SET_DEVICE_EXECUTION_STATUS(e,{deviceId:t,functionType:n,module:s,startTime:o}){n&&s?e.deviceExecutionStatus[t]={functionType:n,module:s,startTime:o||new Date}:delete e.deviceExecutionStatus[t]},CLEAR_DEVICE_EXECUTION_STATUS(e,t){delete e.deviceExecutionStatus[t]},CLEAR_ALL_DEVICE_EXECUTION_STATUS(e){e.deviceExecutionStatus={}}},p={async fetchDevices({commit:e}){e("SET_LOADING",!0);try{const t=await i.A.get("/api/device/list");if(t.data.success){const n=t.data.data.map(e=>({device_id:e.deviceId,device_name:e.deviceName,device_info:e.deviceInfo,status:e.status,last_seen:e.lastActiveTime,created_at:e.createdAt,ip_address:e.deviceIP||"未知",is_connected:e.isConnected}));console.log("[Device Store] 设备列表字段映射完成:",n.length),e("SET_DEVICES",n)}}catch(t){console.error("获取设备列表失败:",t)}finally{e("SET_LOADING",!1)}},async deleteDevice({commit:e},t){try{const n=await i.A.delete(`/api/device/${t}`);if(n.data.success)return e("UPDATE_DEVICE",{deviceId:t,updates:{status:"offline"}}),{success:!0,message:n.data.message}}catch(n){throw n}},async deleteDeviceRecord({commit:e},t){try{const n=await i.A.delete(`/api/device/${t}/delete`);if(n.data.success)return e("REMOVE_DEVICE",t),{success:!0,message:n.data.message}}catch(n){throw n}},async getDeviceDetail({commit:e},t){try{const e=await i.A.get(`/api/device/${t}`);if(e.data.success)return e.data.data}catch(n){throw n}},async getDeviceLogs({commit:e},{deviceId:t,page:n=1,limit:s=20}){try{const e=await i.A.get(`/api/device/${t}/logs`,{params:{page:n,limit:s}});if(e.data.success)return e.data.data}catch(o){throw o}},deviceOnline({commit:e},t){e("ADD_DEVICE",{...t,status:"online"})},deviceOffline({commit:e},t){e("UPDATE_DEVICE",{deviceId:t,updates:{status:"offline"}})},updateDeviceStatus({commit:e},{deviceId:t,status:n,...s}){e("UPDATE_DEVICE",{deviceId:t,updates:{status:n,...s}})},setSelectedDevices({commit:e},t){e("SET_SELECTED_DEVICES",t)},setDeviceExecutionStatus({commit:e},{deviceId:t,functionType:n,module:s,startTime:o}){e("SET_DEVICE_EXECUTION_STATUS",{deviceId:t,functionType:n,module:s,startTime:o})},clearDeviceExecutionStatus({commit:e},t){e("CLEAR_DEVICE_EXECUTION_STATUS",t)},clearAllDeviceExecutionStatus({commit:e}){e("CLEAR_ALL_DEVICE_EXECUTION_STATUS")}};var S={namespaced:!0,state:g,getters:h,mutations:m,actions:p};const T={scripts:[],currentScript:null,loading:!1},f={scripts:e=>e.scripts,currentScript:e=>e.currentScript,loading:e=>e.loading},E={SET_SCRIPTS(e,t){e.scripts=t},ADD_SCRIPT(e,t){e.scripts.unshift(t)},UPDATE_SCRIPT(e,t){const n=e.scripts.findIndex(e=>e.id===t.id);n>=0&&e.scripts.splice(n,1,t)},REMOVE_SCRIPT(e,t){const n=e.scripts.findIndex(e=>e.id===t);n>=0&&e.scripts.splice(n,1)},SET_CURRENT_SCRIPT(e,t){e.currentScript=t},SET_LOADING(e,t){e.loading=t}},v={async fetchScripts({commit:e}){e("SET_LOADING",!0);try{const t=await i.A.get("/api/script/list");t.data.success&&e("SET_SCRIPTS",t.data.data)}catch(t){console.error("获取脚本列表失败:",t)}finally{e("SET_LOADING",!1)}},async getScript({commit:e},t){try{const n=await i.A.get(`/api/script/${t}`);if(n.data.success)return e("SET_CURRENT_SCRIPT",n.data.data),n.data.data}catch(n){throw n}},async createScript({commit:e},t){try{const n=await i.A.post("/api/script/create",t);if(n.data.success){const s={...t,id:n.data.data.id};return e("ADD_SCRIPT",s),{success:!0,message:n.data.message,data:s}}}catch(n){throw n}},async updateScript({commit:e},{id:t,...n}){try{const s=await i.A.put(`/api/script/${t}`,n);if(s.data.success){const o={id:t,...n};return e("UPDATE_SCRIPT",o),{success:!0,message:s.data.message}}}catch(s){throw s}},async deleteScript({commit:e},t){try{const n=await i.A.delete(`/api/script/${t}`);if(n.data.success)return e("REMOVE_SCRIPT",t),{success:!0,message:n.data.message}}catch(n){throw n}},async executeScript({commit:e},{deviceIds:t,scriptId:n,params:s}){try{const e=await i.A.post("/api/script/execute",{deviceIds:t,scriptId:n,params:s});if(e.data.success)return{success:!0,message:e.data.message,data:e.data.data}}catch(o){throw o}},async getExecutionLogs({commit:e},{page:t=1,limit:n=20,deviceId:s,status:o}){try{const e=await i.A.get("/api/script/logs/list",{params:{page:t,limit:n,deviceId:s,status:o}});if(e.data.success)return e.data.data}catch(c){throw c}}};var k={namespaced:!0,state:T,getters:f,mutations:E,actions:v};const _={connected:!1,socket:null,websocketManager:null,deviceDisconnectTimeouts:new Map,deviceDisconnectCountdowns:new Map},C={connected:e=>e.connected,socket:e=>e.socket,websocketManager:e=>e.websocketManager},I={SET_CONNECTED(e,t){e.connected=t},SET_SOCKET(e,t){e.socket=t},SET_WEBSOCKET_MANAGER(e,t){e.websocketManager=t},SET_DEVICE_DISCONNECT_COUNTDOWN(e,{deviceId:t,countdownInfo:n}){e.deviceDisconnectCountdowns.set(t,n)},CLEAR_DEVICE_DISCONNECT_COUNTDOWN(e,t){e.deviceDisconnectCountdowns.delete(t)}},b={connect({commit:e,dispatch:t,rootGetters:n,rootState:s},o){e("SET_SOCKET",o),o.on("connect",()=>{console.log("WebSocket连接成功"),e("SET_CONNECTED",!0);const t=n["auth/user"],s=n["auth/isAuthenticated"];if(console.log("🔍 [WebSocket] 用户认证状态检查:"),console.log("🔍 [WebSocket] isAuthenticated:",s),console.log("🔍 [WebSocket] user:",t),s&&t)console.log("🔍 [WebSocket] 发送认证用户连接:",{userId:t.id,username:t.username}),o.emit("web_client_connect",{userId:t.id,username:t.username}),console.log("已注册为认证Web客户端:",t.username);else{const e="anonymous_"+Date.now();console.log("🔍 [WebSocket] 发送匿名用户连接:",{userId:e,username:"anonymous",clientType:"anonymous_web"}),o.emit("web_client_connect",{userId:e,username:"anonymous",clientType:"anonymous_web"}),console.log("已注册为匿名Web客户端")}}),o.on("disconnect",()=>{console.log("WebSocket连接断开"),e("SET_CONNECTED",!1)}),o.on("devices_list",t=>{e("device/SET_DEVICES",t,{root:!0})}),o.on("device_online",t=>{e("device/ADD_DEVICE",t,{root:!0})}),o.on("device_offline",({deviceId:t})=>{e("device/UPDATE_DEVICE",{deviceId:t,updates:{status:"offline"}},{root:!0})}),o.on("script_result",e=>{console.log("脚本执行结果:",e)}),o.on("xiaohongshu_execution_completed",t=>{console.log("小红书脚本执行完成:",t),e("xiaohongshu/RESET_EXECUTION_STATE",t,{root:!0})}),o.on("xianyu_execution_completed",t=>{console.log("闲鱼脚本执行完成:",t),e("xianyu/RESET_EXECUTION_STATE",t,{root:!0})}),o.on("xiaohongshu_script_reset",t=>{console.log("[Socket] 收到小红书脚本重置事件:",t),e("xiaohongshu/SET_FUNCTION_STATE",{functionType:t.functionType,stateData:{isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null}},{root:!0})}),o.on("xiaohongshu_vuex_state_update",t=>{console.log("[Socket] 收到小红书Vuex状态更新事件:",t),"stopTask"===t.action&&e("xiaohongshu/STOP_TASK",{functionType:t.functionType,reason:t.reason||"server_stop"},{root:!0})}),o.on("device_status_update",t=>{console.log("[Socket] 收到设备状态更新事件:",t),e("device/UPDATE_DEVICE_STATUS",t,{root:!0}),e("device/UPDATE_DEVICE",{deviceId:t.deviceId,updates:{status:t.status,last_seen:t.lastSeen||new Date}},{root:!0})}),o.on("xiaohongshu_execution_update",t=>{console.log("[Socket] 收到小红书执行状态更新:",t),e("xiaohongshu/UPDATE_EXECUTION_STATUS",{taskId:t.taskId,deviceId:t.deviceId,status:t.status,progress:t.progress,stage:t.stage,message:t.message,timestamp:t.timestamp},{root:!0})}),o.on("device_execution_reset",t=>{console.log("[Socket] 收到设备执行重置事件:",t),"xiaohongshu"===t.type?e("xiaohongshu/RESET_DEVICE_EXECUTION",{deviceId:t.deviceId,reason:t.reason},{root:!0}):"xianyu"===t.type&&e("xianyu/RESET_DEVICE_EXECUTION",{deviceId:t.deviceId,reason:t.reason},{root:!0})}),o.on("device_status_changed",n=>{if(console.log("[Socket] 收到设备状态变化事件:",n),"device_connected"===n.type)console.log("[Socket] 设备重新连接，取消断开连接超时处理:",n.deviceId),t("cancelDeviceDisconnectTimeout",n.deviceId),e("device/UPDATE_DEVICE_STATUS",{deviceId:n.deviceId,status:"online",lastSeen:n.timestamp||new Date},{root:!0}),console.log("[Socket] 设备重新连接处理完成:",n.deviceId);else if("device_disconnected"===n.type){console.log("[Socket] 设备断开连接，开始清理相关状态:",n.deviceId),e("device/UPDATE_DEVICE_STATUS",{deviceId:n.deviceId,status:"offline",lastSeen:n.timestamp||new Date},{root:!0}),e("xiaohongshu/CLEAR_DEVICE_STATE",{deviceId:n.deviceId},{root:!0}),e("xianyu/CLEAR_DEVICE_STATE",{deviceId:n.deviceId},{root:!0});const o=s.device.selectedDevices||[],i=o.filter(e=>e!==n.deviceId);i.length!==o.length&&e("device/SET_SELECTED_DEVICES",i,{root:!0}),t("startDeviceDisconnectTimeout",{deviceId:n.deviceId,deviceName:n.deviceName||"未知设备"}),console.log("[Socket] 设备断开连接状态清理完成:",n.deviceId)}}),o.on("device_offline",e=>{console.log("[Socket] 收到设备离线事件:",e);const t={type:"device_disconnected",deviceId:e.deviceId,timestamp:new Date};o.emit("device_status_changed",t)})},disconnect({commit:e,state:t}){t.socket&&(t.socket.disconnect(),e("SET_SOCKET",null),e("SET_CONNECTED",!1))},startDeviceDisconnectTimeout({commit:e,state:t,dispatch:n},{deviceId:s,deviceName:o}){if(console.log(`[Socket] 启动设备断开连接超时处理: ${s} (${o})`),t.deviceDisconnectTimeouts.has(s)){clearTimeout(t.deviceDisconnectTimeouts.get(s));const e=t.deviceDisconnectCountdowns.get(s);e?.intervalId&&clearInterval(e.intervalId)}let i=60;const c={deviceId:s,deviceName:o,remainingSeconds:i,intervalId:null},a=()=>{c.remainingSeconds=i,e("SET_DEVICE_DISCONNECT_COUNTDOWN",{deviceId:s,countdownInfo:{...c}}),i<=0?n("handleDeviceDisconnectTimeout",s):i--};a();const r=setInterval(a,1e3);c.intervalId=r;const l=setTimeout(()=>{n("handleDeviceDisconnectTimeout",s)},6e4);t.deviceDisconnectTimeouts.set(s,l),t.deviceDisconnectCountdowns.set(s,c)},handleDeviceDisconnectTimeout({commit:e,state:t},n){if(console.log(`[Socket] 设备断开连接超时处理: ${n}`),t.deviceDisconnectTimeouts.has(n)&&(clearTimeout(t.deviceDisconnectTimeouts.get(n)),t.deviceDisconnectTimeouts.delete(n)),t.deviceDisconnectCountdowns.has(n)){const e=t.deviceDisconnectCountdowns.get(n);e.intervalId&&clearInterval(e.intervalId),t.deviceDisconnectCountdowns.delete(n)}e("CLEAR_DEVICE_DISCONNECT_COUNTDOWN",n),e("xiaohongshu/FORCE_STOP_DEVICE_EXECUTION",{deviceId:n},{root:!0}),e("xianyu/FORCE_STOP_DEVICE_EXECUTION",{deviceId:n},{root:!0}),console.log(`[Socket] 设备 ${n} 断开连接超时处理完成`)},cancelDeviceDisconnectTimeout({commit:e,state:t},n){if(console.log(`[Socket] 取消设备断开连接超时处理: ${n}`),t.deviceDisconnectTimeouts.has(n)&&(clearTimeout(t.deviceDisconnectTimeouts.get(n)),t.deviceDisconnectTimeouts.delete(n)),t.deviceDisconnectCountdowns.has(n)){const e=t.deviceDisconnectCountdowns.get(n);e.intervalId&&clearInterval(e.intervalId),t.deviceDisconnectCountdowns.delete(n)}e("CLEAR_DEVICE_DISCONNECT_COUNTDOWN",n)}};var y={namespaced:!0,state:_,getters:C,mutations:I,actions:b};const D="xiaohongshu_function_states",A=()=>{try{const e=localStorage.getItem(D);if(e){const t=JSON.parse(e);return console.log("从localStorage恢复小红书状态:",t),t}}catch(e){console.error("从localStorage恢复状态失败:",e)}return{}},w=e=>{try{localStorage.setItem(D,JSON.stringify(e)),console.log("小红书状态已保存到localStorage")}catch(t){console.error("保存状态到localStorage失败:",t)}},x=()=>({isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null,realtimeData:{}}),R=A(),O={globalExecutionState:{isAnyTaskRunning:!1,runningTasks:[],lastUpdateTime:null},functionStates:{profile:{...x(),...R.profile||{}},searchGroupChat:{...x(),...R.searchGroupChat||{}},groupMessage:{...x(),sentMessageCount:0,processedControlCount:0,executionCount:0,loopCount:0,currentStatus:"等待开始",...R.groupMessage||{}},articleComment:{...x(),...R.articleComment||{}},uidMessage:{...x(),...R.uidMessage||{}},uidFileMessage:{...x(),...R.uidFileMessage||{}},videoPublish:{...x(),...R.videoPublish||{}}},pageState:{selectedFunction:"",selectedDevices:[],activeDeviceTab:"",deviceConfigs:{},forceBatchStopDisplay:{},showRealtimeStatus:!1,executingDevices:[],lastVisitTime:null}},j={SET_GLOBAL_EXECUTION_STATE(e,{isAnyTaskRunning:t,runningTasks:n}){e.globalExecutionState.isAnyTaskRunning=t,e.globalExecutionState.runningTasks=n||[],e.globalExecutionState.lastUpdateTime=(new Date).toISOString()},SET_FUNCTION_STATE(e,{functionType:t,stateData:n}){e.functionStates[t]&&(e.functionStates[t]={...e.functionStates[t],...n},w(e.functionStates))},START_TASK(e,{functionType:t,taskId:n,config:s,selectedDevices:o,mergeDevices:i=!1}){const c=e.functionStates[t];if(c){if(c.isScriptRunning=!0,c.isScriptCompleted=!1,c.taskId=n,c.config=s,i&&c.selectedDevices){const e=c.selectedDevices||[],n=o||[];c.selectedDevices=[...new Set([...e,...n])],console.log(`[Vuex] 合并设备列表: ${t}`,c.selectedDevices)}else c.selectedDevices=o||[];c.startTime=(new Date).toISOString(),c.status="running",c.progress=0,c.logs=[]}const a=e.globalExecutionState.runningTasks.filter(e=>e.functionType!==t);a.push({functionType:t,taskId:n,startTime:(new Date).toISOString()}),e.globalExecutionState.isAnyTaskRunning=!0,e.globalExecutionState.runningTasks=a,e.globalExecutionState.lastUpdateTime=(new Date).toISOString(),w(e.functionStates)},STOP_TASK(e,{functionType:t,reason:n="manual"}){const s=e.functionStates[t];s&&(s.isScriptRunning=!1,s.status="completed"===n?"completed":"stopped",s.progress="completed"===n?100:s.progress,"completed"===n&&(s.isScriptCompleted=!0,setTimeout(()=>{e.functionStates[t]&&(e.functionStates[t].isScriptCompleted=!1)},6e4)));const o=e.globalExecutionState.runningTasks.filter(e=>e.functionType!==t);e.globalExecutionState.runningTasks=o,e.globalExecutionState.isAnyTaskRunning=o.length>0,e.globalExecutionState.lastUpdateTime=(new Date).toISOString()},UPDATE_TASK_PROGRESS(e,{functionType:t,progress:n,status:s,logs:o}){const i=e.functionStates[t];i&&(void 0!==n&&(i.progress=n),void 0!==s&&(i.status=s),void 0!==o&&(Array.isArray(o)?i.logs=o:i.logs.push({timestamp:(new Date).toISOString(),message:o})))},UPDATE_GROUP_MESSAGE_STATE(e,{sentMessageCount:t,processedControlCount:n,executionCount:s,loopCount:o,currentStatus:i}){const c=e.functionStates.groupMessage;c&&(void 0!==t&&(c.sentMessageCount=t),void 0!==n&&(c.processedControlCount=n),void 0!==s&&(c.executionCount=s),void 0!==o&&(c.loopCount=o),void 0!==i&&(c.currentStatus=i))},SET_PAGE_STATE(e,{selectedFunction:t,selectedDevices:n,activeDeviceTab:s,deviceConfigs:o,forceBatchStopDisplay:i,showRealtimeStatus:c,executingDevices:a}){void 0!==t&&(e.pageState.selectedFunction=t),void 0!==n&&(e.pageState.selectedDevices=n),void 0!==s&&(e.pageState.activeDeviceTab=s),void 0!==o&&(e.pageState.deviceConfigs=o),void 0!==i&&(e.pageState.forceBatchStopDisplay=i),void 0!==c&&(e.pageState.showRealtimeStatus=c),void 0!==a&&(e.pageState.executingDevices=a),e.pageState.lastVisitTime=(new Date).toISOString()},RESET_FUNCTION_STATE(e,t){const n=x();"groupMessage"===t&&(n.sentMessageCount=0,n.processedControlCount=0,n.executionCount=0,n.loopCount=0,n.currentStatus="等待开始"),e.functionStates[t]=n,w(e.functionStates)},RESET_EXECUTION_STATE(e,{deviceId:t,taskId:n,status:s,message:o}){console.log("[Vuex] 重置脚本执行状态:",{deviceId:t,taskId:n,status:s,message:o});let i=null;n&&(n.includes("profile")?i="profile":n.includes("searchGroupChat")||n.includes("group_chat")?i="searchGroupChat":n.includes("groupMessage")?i="groupMessage":n.includes("articleComment")?i="articleComment":n.includes("uidFileMessage")?i="uidFileMessage":n.includes("uidMessage")&&(i="uidMessage")),i&&e.functionStates[i]&&(e.functionStates[i].isScriptRunning=!1,e.functionStates[i].isScriptCompleted=!0,e.functionStates[i].status="success"===s?"completed":"failed",e.functionStates[i].progress="success"===s?100:0,e.functionStates[i].lastResult=o,console.log(`[Vuex] 功能 ${i} 执行状态已重置为: ${e.functionStates[i].status}`));const c=e.globalExecutionState.runningTasks.filter(e=>e.taskId!==n);e.globalExecutionState.runningTasks=c,e.globalExecutionState.isAnyTaskRunning=c.length>0,e.globalExecutionState.lastUpdateTime=(new Date).toISOString()},CLEAR_DEVICE_STATE(e,{deviceId:t}){if(console.log("[小红书Vuex] 清理设备状态:",t),Object.keys(e.functionStates).forEach(n=>{const s=e.functionStates[n];if(s.selectedDevices&&s.selectedDevices.includes(t)){const e=s.selectedDevices.indexOf(t);s.selectedDevices.splice(e,1),console.log(`[小红书Vuex] 从功能 ${n} 的选中设备中移除: ${t}`)}s.isScriptRunning&&s.selectedDevices&&0===s.selectedDevices.length&&(console.log(`[小红书Vuex] 重置功能 ${n} 的执行状态（设备断开）`),s.isScriptRunning=!1,s.isScriptCompleted=!1,s.taskId=null,s.config={},s.startTime=null,s.progress=0,s.status="idle",s.logs=[],s.lastResult=null,s.realtimeData={})}),e.pageState.selectedDevices&&e.pageState.selectedDevices.includes(t)){const n=e.pageState.selectedDevices.indexOf(t);e.pageState.selectedDevices.splice(n,1),console.log(`[小红书Vuex] 从页面状态的选中设备中移除: ${t}`)}e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[小红书Vuex] 清理设备 ${t} 的当前任务`)),w(e.functionStates)},FORCE_STOP_DEVICE_EXECUTION(e,{deviceId:t}){console.log("[小红书Vuex] 强制停止设备执行状态显示:",t),Object.keys(e.functionStates).forEach(t=>{const n=e.functionStates[t];n.isScriptRunning&&(console.log(`[小红书Vuex] 强制停止功能 ${t} 的执行状态（设备断开连接超时）`),n.isScriptRunning=!1,n.isScriptCompleted=!1,n.taskId=null,n.progress=0,n.status="idle",n.logs=[],n.lastResult=null,n.realtimeData={})}),e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[小红书Vuex] 清理设备 ${t} 的当前任务（强制停止）`)),w(e.functionStates)},UPDATE_EXECUTION_STATUS(e,{taskId:t,deviceId:n,status:s,progress:o,stage:i,message:c,timestamp:a}){"completed"!==s&&"failed"!==s&&"stopped"!==s||console.log("[小红书Vuex] 更新执行状态:",{taskId:t,deviceId:n,status:s,progress:o,stage:i,message:c}),Object.keys(e.functionStates).forEach(i=>{const r=e.functionStates[i];(r.taskId===t||r.isScriptRunning&&n)&&(console.log(`[小红书Vuex] 更新功能 ${i} 的执行状态`),"failed"===s||"error"===s?(r.isScriptRunning=!1,r.isScriptCompleted=!1,r.progress=0,r.status="error",r.logs.push({timestamp:a||new Date,level:"error",message:c||"执行失败"})):"completed"===s||"success"===s?(r.isScriptRunning=!1,r.isScriptCompleted=!0,r.progress=100,r.status="completed",r.logs.push({timestamp:a||new Date,level:"success",message:c||"执行完成"})):(r.progress=o||r.progress,r.status=s||r.status,c&&r.logs.push({timestamp:a||new Date,level:"info",message:c})))}),w(e.functionStates)},RESET_DEVICE_EXECUTION(e,{deviceId:t,reason:n}){console.log("[小红书Vuex] 重置设备执行状态:",{deviceId:t,reason:n}),Object.keys(e.functionStates).forEach(t=>{const s=e.functionStates[t];s.isScriptRunning&&(console.log(`[小红书Vuex] 重置功能 ${t} 的执行状态（${n}）`),s.isScriptRunning=!1,s.isScriptCompleted=!1,s.progress=0,s.status="error",s.logs.push({timestamp:new Date,level:"error",message:"device_disconnected"===n?"设备断开连接，任务执行失败":"任务执行被重置"}))}),e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[小红书Vuex] 清理设备 ${t} 的当前任务（重置执行状态）`)),w(e.functionStates)}},N={async startTask({commit:e},{functionType:t,taskId:n,config:s,selectedDevices:o,mergeDevices:i=!1}){console.log(`[Vuex] 启动任务: ${t}`,{taskId:n,config:s,selectedDevices:o,mergeDevices:i}),e("START_TASK",{functionType:t,taskId:n,config:s,selectedDevices:o,mergeDevices:i})},async stopTask({commit:e},{functionType:t,reason:n="manual"}){console.log(`[Vuex] 停止任务: ${t}`,{reason:n}),e("STOP_TASK",{functionType:t,reason:n})},async updateTaskProgress({commit:e},{functionType:t,progress:n,status:s,logs:o}){e("UPDATE_TASK_PROGRESS",{functionType:t,progress:n,status:s,logs:o})},async updateGroupMessageState({commit:e},t){e("UPDATE_GROUP_MESSAGE_STATE",t)},async setPageState({commit:e},t){e("SET_PAGE_STATE",t)},async setFunctionState({commit:e},{functionType:t,stateData:n}){e("SET_FUNCTION_STATE",{functionType:t,stateData:n})},async resetFunctionState({commit:e},t){console.log(`[Vuex] 重置功能状态: ${t}`),e("RESET_FUNCTION_STATE",t)},async checkAndRestoreRunningTasks({commit:e,state:t,rootState:n}){console.log("[Vuex] 检查并恢复运行中的任务");try{const s=n.auth.token;if(!s)return void console.warn("[Vuex] 未登录，无法检查运行中任务");const o=await fetch("/api/xiaohongshu/logs?executionStatus=running&limit=50",{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const i=await o.json();if(i.success&&i.data.logs){const t=i.data.logs;t.length>0&&console.log("[Vuex] 发现所有任务:",t.length,"个");const n=t.filter(e=>{const t=e.execution_status,n="running"===t||"pending"===t,s=new Date(e.started_at||e.created_at),o=new Date(Date.now()-3e5),i=s>o;return n&&i&&console.log(`[Vuex] 任务 ${e.task_id} 状态检查:`,{status:t,isRunning:n,lastUpdate:s.toISOString(),isRecent:i,shouldRestore:n&&i}),n&&i});n.length>0&&console.log("[Vuex] 真正运行中的任务:",n.length,"个"),e("SET_GLOBAL_EXECUTION_STATE",{isAnyTaskRunning:n.length>0,runningTasks:n,lastUpdateTime:new Date});const s={};n.forEach(e=>{const t=e.function_type;s[t]||(s[t]=[]),s[t].push(e)}),Object.keys(s).forEach(t=>{const n=s[t],o=n[0];console.log(`[Vuex] 恢复功能 ${t} 的运行状态:`,o),e("SET_FUNCTION_STATE",{functionType:t,stateData:{isScriptRunning:!0,isScriptCompleted:!1,taskId:o.task_id,config:o.config_params||{},selectedDevices:[o.device_id],startTime:o.started_at,progress:o.progress_percentage||0,status:"running",logs:[],lastResult:null}})}),console.log("[Vuex] 任务状态恢复完成")}else console.log("[Vuex] 没有发现运行中的任务"),e("SET_GLOBAL_EXECUTION_STATE",{isAnyTaskRunning:!1,runningTasks:[],lastUpdateTime:new Date});console.log("[Vuex] 当前功能状态:",t.functionStates)}catch(s){console.error("[Vuex] 检查运行中任务失败:",s)}},clearDeviceExecutionState({commit:e,state:t},n){console.log(`[Vuex] 清理设备 ${n} 的执行状态`),Object.keys(t.functionStates).forEach(s=>{const o=t.functionStates[s];if(o.selectedDevices&&o.selectedDevices.includes(n)){console.log(`[Vuex] 清理功能 ${s} 中设备 ${n} 的状态`);const t=o.selectedDevices.filter(e=>e!==n);0===t.length?(console.log(`[Vuex] 功能 ${s} 没有其他设备，重置功能状态`),e("SET_FUNCTION_STATE",{functionType:s,stateData:{isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null}})):e("SET_FUNCTION_STATE",{functionType:s,stateData:{...o,selectedDevices:t}})}})},resetGlobalExecutionState({commit:e}){console.log("[Vuex] 重置全局执行状态"),e("SET_GLOBAL_EXECUTION_STATE",{isAnyTaskRunning:!1,runningTasks:[],lastUpdateTime:new Date})},forceResetAllFunctionStates({commit:e}){console.log("[Vuex] 强制重置所有功能状态");const t=["profile","searchGroupChat","groupMessage","articleComment","uidMessage","uidFileMessage","videoPublish"];t.forEach(t=>{e("SET_FUNCTION_STATE",{functionType:t,stateData:{isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null}})}),e("SET_GLOBAL_EXECUTION_STATE",{isAnyTaskRunning:!1,runningTasks:[],lastUpdateTime:new Date}),console.log("[Vuex] 所有功能状态已强制重置")}},M={getFunctionState:e=>t=>e.functionStates[t]||{},isAnyTaskRunning:e=>e.globalExecutionState.isAnyTaskRunning,getRunningTasks:e=>e.globalExecutionState.runningTasks,getPageState:e=>e.pageState,isFunctionRunning:e=>t=>{const n=e.functionStates[t];return!!n&&n.isScriptRunning},isFunctionCompleted:e=>t=>{const n=e.functionStates[t];return!!n&&n.isScriptCompleted},getFunctionConfig:e=>t=>{const n=e.functionStates[t];return n?n.config:{}},getDeviceTasks:e=>t=>{const n=[];return Object.keys(e.functionStates).forEach(s=>{const o=e.functionStates[s];o.isScriptRunning&&o.selectedDevices&&o.selectedDevices.includes(t)&&n.push({functionType:s,taskId:o.taskId,status:o.status,startTime:o.startTime,progress:o.progress})}),n}};var V={namespaced:!0,state:O,mutations:j,actions:N,getters:M};const U="xianyu_function_states",W=()=>{try{const e=localStorage.getItem(U);if(e){const t=JSON.parse(e);return console.log("从localStorage恢复闲鱼状态:",t),t}}catch(e){console.error("从localStorage恢复闲鱼状态失败:",e)}return{}},P=e=>{try{localStorage.setItem(U,JSON.stringify(e)),console.log("闲鱼状态已保存到localStorage")}catch(t){console.error("保存闲鱼状态到localStorage失败:",t)}},$=()=>({isScriptRunning:!1,isScriptCompleted:!1,taskId:null,config:{},selectedDevices:[],startTime:null,progress:0,status:"idle",logs:[],lastResult:null,realtimeData:{}}),L=W(),H={functionStates:{keywordMessage:{...$(),...L.keywordMessage||{}}},executionLogs:[],currentTasks:{}},F={getFunctionState:e=>t=>e.functionStates[t]||{},isFunctionRunning:e=>t=>{const n=e.functionStates[t];return!!n&&n.isScriptRunning},isFunctionCompleted:e=>t=>{const n=e.functionStates[t];return!!n&&n.isScriptCompleted},getExecutionLogs:e=>e.executionLogs,getCurrentTasks:e=>e.currentTasks,getDeviceTasks:e=>t=>{const n=[];if(e.currentTasks[t]){const s=e.currentTasks[t];n.push({functionType:s.functionType||"keywordMessage",taskId:s.taskId,status:s.status||"running",startTime:s.startTime,progress:s.progress||0})}return Object.keys(e.functionStates).forEach(s=>{const o=e.functionStates[s];if(o.isScriptRunning&&e.currentTasks[t]&&e.currentTasks[t].functionType===s){const i=n.find(e=>e.functionType===s&&e.taskId===o.currentTaskId);i||n.push({functionType:s,taskId:o.currentTaskId,status:"running",startTime:e.currentTasks[t].startTime||(new Date).toISOString(),progress:0})}}),n.length>0&&console.log(`[闲鱼Vuex] 设备 ${t} 的任务列表:`,n),n}},z={SET_FUNCTION_STATE(e,{functionType:t,stateData:n}){e.functionStates[t]||(e.functionStates[t]=$()),Object.assign(e.functionStates[t],n),P(e.functionStates)},RESET_FUNCTION_STATE(e,t){e.functionStates[t]&&(e.functionStates[t]=$()),P(e.functionStates)},SET_SCRIPT_RUNNING(e,{functionType:t,isRunning:n,taskId:s=null,logId:o=null}){e.functionStates[t]||(e.functionStates[t]={isScriptRunning:!1,isScriptCompleted:!1,config:{},currentLogId:null,currentTaskId:null}),e.functionStates[t].isScriptRunning=n,n?(e.functionStates[t].isScriptCompleted=!1,s&&(e.functionStates[t].currentTaskId=s),o&&(e.functionStates[t].currentLogId=o)):(e.functionStates[t].currentTaskId=null,e.functionStates[t].currentLogId=null)},SET_SCRIPT_COMPLETED(e,{functionType:t,isCompleted:n}){e.functionStates[t]||(e.functionStates[t]={isScriptRunning:!1,isScriptCompleted:!1,config:{},currentLogId:null,currentTaskId:null}),e.functionStates[t].isScriptCompleted=n,n&&(e.functionStates[t].isScriptRunning=!1)},ADD_EXECUTION_LOG(e,t){e.executionLogs.unshift(t),e.executionLogs.length>100&&(e.executionLogs=e.executionLogs.slice(0,100))},SET_CURRENT_TASK(e,{deviceId:t,taskData:n}){e.currentTasks[t]=n},REMOVE_CURRENT_TASK(e,t){delete e.currentTasks[t]},ADD_CURRENT_TASK(e,{taskId:t,task:n}){console.log("[闲鱼Vuex] 添加当前任务:",{taskId:t,task:n}),n&&n.deviceId&&(e.currentTasks[n.deviceId]={taskId:t,functionType:n.functionType,status:n.status||"running",startTime:n.startTime,config:n.config||{}},console.log("[闲鱼Vuex] 当前任务列表已更新:",e.currentTasks))},CLEAR_ALL_CURRENT_TASKS(e){e.currentTasks={}},RESET_EXECUTION_STATE(e,t){console.log("[闲鱼Vuex] 重置执行状态:",t),Object.keys(e.functionStates).forEach(t=>{const n=e.functionStates[t];(n.isScriptRunning||n.isScriptCompleted)&&(console.log(`[闲鱼Vuex] 重置功能 ${t} 的执行状态`),n.isScriptRunning=!1,n.isScriptCompleted=!1,n.currentLogId=null,n.currentTaskId=null)}),t&&t.deviceId&&delete e.currentTasks[t.deviceId]},CLEAR_DEVICE_STATE(e,{deviceId:t}){console.log("[闲鱼Vuex] 清理设备状态:",t),Object.keys(e.functionStates).forEach(n=>{const s=e.functionStates[n];if(s.selectedDevices&&s.selectedDevices.includes(t)){const e=s.selectedDevices.indexOf(t);s.selectedDevices.splice(e,1),console.log(`[闲鱼Vuex] 从功能 ${n} 的选中设备中移除: ${t}`)}s.isScriptRunning&&s.selectedDevices&&0===s.selectedDevices.length&&(console.log(`[闲鱼Vuex] 重置功能 ${n} 的执行状态（设备断开）`),s.isScriptRunning=!1,s.isScriptCompleted=!1,s.taskId=null,s.config={},s.startTime=null,s.progress=0,s.status="idle",s.logs=[],s.lastResult=null,s.realtimeData={})}),e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[闲鱼Vuex] 清理设备 ${t} 的当前任务`)),P(e.functionStates)},FORCE_STOP_DEVICE_EXECUTION(e,{deviceId:t}){console.log("[闲鱼Vuex] 强制停止设备执行状态显示:",t),Object.keys(e.functionStates).forEach(t=>{const n=e.functionStates[t];n.isScriptRunning&&(console.log(`[闲鱼Vuex] 强制停止功能 ${t} 的执行状态（设备断开连接超时）`),n.isScriptRunning=!1,n.isScriptCompleted=!1,n.taskId=null,n.progress=0,n.status="idle",n.logs=[],n.lastResult=null,n.realtimeData={})}),e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[闲鱼Vuex] 清理设备 ${t} 的当前任务（强制停止）`)),P(e.functionStates)},UPDATE_EXECUTION_STATUS(e,{taskId:t,deviceId:n,status:s,progress:o,stage:i,message:c,timestamp:a}){"completed"!==s&&"failed"!==s&&"stopped"!==s||console.log("[闲鱼Vuex] 更新执行状态:",{taskId:t,deviceId:n,status:s,progress:o,stage:i,message:c}),Object.keys(e.functionStates).forEach(i=>{const r=e.functionStates[i];(r.taskId===t||r.isScriptRunning&&n)&&(console.log(`[闲鱼Vuex] 更新功能 ${i} 的执行状态`),"failed"===s||"error"===s?(r.isScriptRunning=!1,r.isScriptCompleted=!1,r.progress=0,r.status="error",r.logs.push({timestamp:a||new Date,level:"error",message:c||"执行失败"})):"completed"===s||"success"===s?(r.isScriptRunning=!1,r.isScriptCompleted=!0,r.progress=100,r.status="completed",r.logs.push({timestamp:a||new Date,level:"success",message:c||"执行完成"})):(r.progress=o||r.progress,r.status=s||r.status,c&&r.logs.push({timestamp:a||new Date,level:"info",message:c})))}),P(e.functionStates)},RESET_DEVICE_EXECUTION(e,{deviceId:t,reason:n}){console.log("[闲鱼Vuex] 重置设备执行状态:",{deviceId:t,reason:n}),Object.keys(e.functionStates).forEach(t=>{const s=e.functionStates[t];s.isScriptRunning&&(console.log(`[闲鱼Vuex] 重置功能 ${t} 的执行状态（${n}）`),s.isScriptRunning=!1,s.isScriptCompleted=!1,s.progress=0,s.status="error",s.logs.push({timestamp:new Date,level:"error",message:"device_disconnected"===n?"设备断开连接，任务执行失败":"任务执行被重置"}))}),e.currentTasks&&e.currentTasks[t]&&(delete e.currentTasks[t],console.log(`[闲鱼Vuex] 清理设备 ${t} 的当前任务（重置执行状态）`)),P(e.functionStates)}},G={setFunctionState({commit:e},{functionType:t,stateData:n}){e("SET_FUNCTION_STATE",{functionType:t,stateData:n})},resetFunctionState({commit:e},t){e("RESET_FUNCTION_STATE",t)},setScriptRunning({commit:e},{functionType:t,isRunning:n,taskId:s,logId:o}){e("SET_SCRIPT_RUNNING",{functionType:t,isRunning:n,taskId:s,logId:o})},setScriptCompleted({commit:e},{functionType:t,isCompleted:n}){e("SET_SCRIPT_COMPLETED",{functionType:t,isCompleted:n})},taskStarted({commit:e},{functionType:t,taskId:n,logId:s,config:o,deviceId:i}){e("SET_SCRIPT_RUNNING",{functionType:t,isRunning:!0,taskId:n,logId:s}),o&&e("SET_FUNCTION_STATE",{functionType:t,stateData:{config:o}}),i&&e("SET_CURRENT_TASK",{deviceId:i,taskData:{functionType:t,taskId:n,status:"running",startTime:(new Date).toISOString(),config:o||{}}})},taskStopped({commit:e},{functionType:t}){e("SET_SCRIPT_RUNNING",{functionType:t,isRunning:!1})},taskCompleted({commit:e},{functionType:t}){e("SET_SCRIPT_COMPLETED",{functionType:t,isCompleted:!0}),setTimeout(()=>{e("SET_SCRIPT_COMPLETED",{functionType:t,isCompleted:!1})},6e4)},addExecutionLog({commit:e},t){const n={id:Date.now(),timestamp:(new Date).toISOString(),...t};e("ADD_EXECUTION_LOG",n)},clearDeviceExecutionState({commit:e,state:t},n){console.log(`[闲鱼Vuex] 清理设备 ${n} 的执行状态`),Object.keys(t.functionStates).forEach(n=>{const s=t.functionStates[n];console.log(`[闲鱼Vuex] 强制重置功能 ${n} 的执行状态`),e("SET_FUNCTION_STATE",{functionType:n,stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:s.config||{},currentLogId:null,currentTaskId:null,progress:0,status:"idle",logs:[],lastResult:null}})}),t.currentTasks[n]&&(console.log(`[闲鱼Vuex] 清理设备 ${n} 的任务`),e("REMOVE_CURRENT_TASK",n))},async restoreExecutionState({commit:e,rootGetters:t}){try{console.log("[闲鱼Vuex] 开始恢复执行状态...");const t=await i.A.get("/api/xianyu/logs",{params:{page:1,limit:50,executionStatus:"running"}}),n=t.data;console.log("[闲鱼Vuex] API返回的完整数据:",n);const s=n.data?.data||n.logs||[];if(console.log("[闲鱼Vuex] 解析出的日志数据:",s),n.success&&s&&s.length>0){console.log(`[闲鱼Vuex] 发现 ${s.length} 个正在执行的任务`),console.log("[闲鱼Vuex] 任务详情:",s),s.forEach((e,t)=>{console.log(`[闲鱼Vuex] 任务 ${t+1} 结构:`,{id:e.id,functionType:e.functionType,deviceInfo:e.deviceInfo,device_id:e.device_id,deviceId:e.deviceId,started_at:e.started_at,config_params:e.config_params})});const t={};s.forEach(e=>{const n=e.functionType||"keywordMessage";t[n]||(t[n]=[]),t[n].push(e)}),Object.keys(t).forEach(n=>{const s=t[n],o=s[0];console.log(`[闲鱼Vuex] 恢复功能 ${n} 的运行状态:`,o);let i={};try{o.configParams?i="string"===typeof o.configParams?JSON.parse(o.configParams):o.configParams:o.config_params&&(i="string"===typeof o.config_params?JSON.parse(o.config_params):o.config_params)}catch(a){console.warn("[闲鱼Vuex] 解析配置参数失败:",a),i={}}e("SET_FUNCTION_STATE",{functionType:n,stateData:{isScriptRunning:!0,isScriptCompleted:!1,currentLogId:o.id,currentTaskId:o.id,config:i}}),console.log(`[闲鱼Vuex] 功能 ${n} 状态已恢复为运行中`),console.log("[闲鱼Vuex] 恢复的配置参数:",i),console.log("[闲鱼Vuex] 准备添加任务到当前任务列表:",o);let c=o.deviceInfo?.id||o.deviceInfo?.device_id||o.device_id||o.deviceId;console.log(`[闲鱼Vuex] 提取的设备ID: ${c}`),c?e("ADD_CURRENT_TASK",{taskId:o.id,task:{deviceId:c,functionType:n,status:"running",startTime:o.started_at,config:o.config_params||{}}}):console.error("[闲鱼Vuex] 无法提取设备ID，任务数据:",o)}),console.log("[闲鱼Vuex] 任务状态恢复完成")}else console.log("[闲鱼Vuex] 没有发现运行中的任务"),console.log("[闲鱼Vuex] 检查条件: success =",n.success,", logs =",s,", length =",s?s.length:"undefined"),console.log("[闲鱼Vuex] 强制清理所有脚本状态，确保与实际状态同步"),Object.keys(H.functionStates).forEach(t=>{console.log(`[闲鱼Vuex] 强制重置功能 ${t} 的状态`),e("SET_FUNCTION_STATE",{functionType:t,stateData:{isScriptRunning:!1,isScriptCompleted:!1,config:H.functionStates[t].config||{},currentLogId:null,currentTaskId:null,progress:0,status:"idle",logs:[],lastResult:null}})}),e("CLEAR_ALL_CURRENT_TASKS"),console.log("[闲鱼Vuex] 强制状态清理完成")}catch(n){console.error("[闲鱼Vuex] 恢复执行状态失败:",n)}},resetAllStates({commit:e}){console.log("[闲鱼Vuex] 重置所有状态"),e("RESET_EXECUTION_STATE","keywordMessage"),e("CLEAR_ALL_CURRENT_TASKS"),console.log("[闲鱼Vuex] 所有状态已重置")}};var X={namespaced:!0,state:H,getters:F,mutations:z,actions:G};s["default"].use(o.Ay);var K=new o.Ay.Store({modules:{auth:d,device:S,script:k,socket:y,xiaohongshu:V,xianyu:X}})},5358:function(e,t,n){var s={"./af":5177,"./af.js":5177,"./ar":1509,"./ar-dz":1488,"./ar-dz.js":1488,"./ar-kw":8676,"./ar-kw.js":8676,"./ar-ly":2353,"./ar-ly.js":2353,"./ar-ma":4496,"./ar-ma.js":4496,"./ar-ps":6947,"./ar-ps.js":6947,"./ar-sa":2682,"./ar-sa.js":2682,"./ar-tn":9756,"./ar-tn.js":9756,"./ar.js":1509,"./az":5533,"./az.js":5533,"./be":8959,"./be.js":8959,"./bg":7777,"./bg.js":7777,"./bm":4903,"./bm.js":4903,"./bn":1290,"./bn-bd":7357,"./bn-bd.js":7357,"./bn.js":1290,"./bo":1545,"./bo.js":1545,"./br":1470,"./br.js":1470,"./bs":4429,"./bs.js":4429,"./ca":7306,"./ca.js":7306,"./cs":6464,"./cs.js":6464,"./cv":3635,"./cv.js":3635,"./cy":4226,"./cy.js":4226,"./da":3601,"./da.js":3601,"./de":7853,"./de-at":6111,"./de-at.js":6111,"./de-ch":4697,"./de-ch.js":4697,"./de.js":7853,"./dv":708,"./dv.js":708,"./el":4691,"./el.js":4691,"./en-au":3872,"./en-au.js":3872,"./en-ca":8298,"./en-ca.js":8298,"./en-gb":6195,"./en-gb.js":6195,"./en-ie":6584,"./en-ie.js":6584,"./en-il":5543,"./en-il.js":5543,"./en-in":9033,"./en-in.js":9033,"./en-nz":9402,"./en-nz.js":9402,"./en-sg":3004,"./en-sg.js":3004,"./eo":2934,"./eo.js":2934,"./es":7650,"./es-do":838,"./es-do.js":838,"./es-mx":7730,"./es-mx.js":7730,"./es-us":6575,"./es-us.js":6575,"./es.js":7650,"./et":3035,"./et.js":3035,"./eu":3508,"./eu.js":3508,"./fa":119,"./fa.js":119,"./fi":527,"./fi.js":527,"./fil":5995,"./fil.js":5995,"./fo":2477,"./fo.js":2477,"./fr":5498,"./fr-ca":6435,"./fr-ca.js":6435,"./fr-ch":7892,"./fr-ch.js":7892,"./fr.js":5498,"./fy":7071,"./fy.js":7071,"./ga":1734,"./ga.js":1734,"./gd":217,"./gd.js":217,"./gl":7329,"./gl.js":7329,"./gom-deva":2124,"./gom-deva.js":2124,"./gom-latn":3383,"./gom-latn.js":3383,"./gu":5050,"./gu.js":5050,"./he":1713,"./he.js":1713,"./hi":3861,"./hi.js":3861,"./hr":3927,"./hr.js":3927,"./hu":609,"./hu.js":609,"./hy-am":7160,"./hy-am.js":7160,"./id":4063,"./id.js":4063,"./is":9374,"./is.js":9374,"./it":8383,"./it-ch":1827,"./it-ch.js":1827,"./it.js":8383,"./ja":3827,"./ja.js":3827,"./jv":9722,"./jv.js":9722,"./ka":1794,"./ka.js":1794,"./kk":7088,"./kk.js":7088,"./km":6870,"./km.js":6870,"./kn":4451,"./kn.js":4451,"./ko":3164,"./ko.js":3164,"./ku":8174,"./ku-kmr":6181,"./ku-kmr.js":6181,"./ku.js":8174,"./ky":8474,"./ky.js":8474,"./lb":9680,"./lb.js":9680,"./lo":5867,"./lo.js":5867,"./lt":5766,"./lt.js":5766,"./lv":9532,"./lv.js":9532,"./me":8076,"./me.js":8076,"./mi":1848,"./mi.js":1848,"./mk":306,"./mk.js":306,"./ml":3739,"./ml.js":3739,"./mn":9053,"./mn.js":9053,"./mr":6169,"./mr.js":6169,"./ms":3386,"./ms-my":2297,"./ms-my.js":2297,"./ms.js":3386,"./mt":7075,"./mt.js":7075,"./my":2264,"./my.js":2264,"./nb":2274,"./nb.js":2274,"./ne":8235,"./ne.js":8235,"./nl":2572,"./nl-be":3784,"./nl-be.js":3784,"./nl.js":2572,"./nn":2185,"./nn.js":2185,"./oc-lnc":9330,"./oc-lnc.js":9330,"./pa-in":9849,"./pa-in.js":9849,"./pl":4418,"./pl.js":4418,"./pt":9834,"./pt-br":8303,"./pt-br.js":8303,"./pt.js":9834,"./ro":4457,"./ro.js":4457,"./ru":2271,"./ru.js":2271,"./sd":1221,"./sd.js":1221,"./se":3478,"./se.js":3478,"./si":7538,"./si.js":7538,"./sk":5784,"./sk.js":5784,"./sl":6637,"./sl.js":6637,"./sq":6794,"./sq.js":6794,"./sr":5719,"./sr-cyrl":3322,"./sr-cyrl.js":3322,"./sr.js":5719,"./ss":6e3,"./ss.js":6e3,"./sv":1011,"./sv.js":1011,"./sw":748,"./sw.js":748,"./ta":1025,"./ta.js":1025,"./te":1885,"./te.js":1885,"./tet":8861,"./tet.js":8861,"./tg":6571,"./tg.js":6571,"./th":5802,"./th.js":5802,"./tk":9527,"./tk.js":9527,"./tl-ph":9231,"./tl-ph.js":9231,"./tlh":8671,"./tlh.js":8671,"./tr":5096,"./tr.js":5096,"./tzl":9846,"./tzl.js":9846,"./tzm":1765,"./tzm-latn":7711,"./tzm-latn.js":7711,"./tzm.js":1765,"./ug-cn":8414,"./ug-cn.js":8414,"./uk":6618,"./uk.js":6618,"./ur":158,"./ur.js":158,"./uz":7609,"./uz-latn":2475,"./uz-latn.js":2475,"./uz.js":7609,"./vi":1135,"./vi.js":1135,"./x-pseudo":4051,"./x-pseudo.js":4051,"./yo":2218,"./yo.js":2218,"./zh-cn":2648,"./zh-cn.js":2648,"./zh-hk":1632,"./zh-hk.js":1632,"./zh-mo":1541,"./zh-mo.js":1541,"./zh-tw":304,"./zh-tw.js":304};function o(e){var t=i(e);return n(t)}function i(e){if(!n.o(s,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return s[e]}o.keys=function(){return Object.keys(s)},o.resolve=i,e.exports=o,o.id=5358},5596:function(e,t,n){"use strict";n.d(t,{restoreComponentState:function(){return a},saveComponentState:function(){return c}});const s=(e,t,n)=>{try{const s=`${e}_backup_${t||"default"}`,o={...n,timestamp:Date.now()};localStorage.setItem(s,JSON.stringify(o)),console.log(`[StateManager] ${e} 状态已保存到localStorage备份`)}catch(s){console.error(`[StateManager] 保存 ${e} 状态备份失败:`,s)}},o=(e,t)=>{try{const n=`${e}_backup_${t||"default"}`,s=localStorage.getItem(n);if(s){const t=JSON.parse(s);return console.log(`[StateManager] 从localStorage备份恢复 ${e} 状态:`,t),t}}catch(n){console.error(`[StateManager] 恢复 ${e} 状态备份失败:`,n)}return null},i=(e=864e5)=>{try{const n=Date.now(),s=[];for(let o=0;o<localStorage.length;o++){const i=localStorage.key(o);if(i&&i.includes("_backup_"))try{const t=JSON.parse(localStorage.getItem(i));t.timestamp&&n-t.timestamp>e&&s.push(i)}catch(t){s.push(i)}}s.forEach(e=>{localStorage.removeItem(e),console.log(`[StateManager] 清理过期备份: ${e}`)}),s.length>0&&console.log(`[StateManager] 已清理 ${s.length} 个过期备份`)}catch(n){console.error("[StateManager] 清理过期备份失败:",n)}},c=async(e,t,n)=>{try{await e.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:n}),s(t,e.deviceId,n),console.log(`[StateManager] ${t} 组件状态已保存到Vuex和localStorage`)}catch(o){console.error(`[StateManager] 保存 ${t} 组件状态失败:`,o)}},a=async(e,t)=>{try{let n=e.$store.getters["xiaohongshu/getFunctionState"](t);if(!n||0===Object.keys(n).length){const s=o(t,e.deviceId);s&&(n=s,await e.$store.dispatch("xiaohongshu/setFunctionState",{functionType:t,stateData:s}),console.log(`[StateManager] ${t} 备份状态已同步到Vuex`))}return n}catch(n){return console.error(`[StateManager] 恢复 ${t} 组件状态失败:`,n),null}};"undefined"!==typeof window&&setTimeout(()=>{i()},1e3)},6006:function(e,t,n){"use strict";n.d(t,{MC:function(){return r},ensureConnection:function(){return u},getWebSocketManager:function(){return l}});var s=n(4787),o=n(4310),i=n(9381);class c{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectDelay=1e3,this.maxReconnectDelay=3e4,this.eventHandlers=new Map,this.isInitialized=!1,this.reconnectTimer=null,this.heartbeatTimer=null,this.heartbeatInterval=15e3,this.connectionPromise=null,this.lastHeartbeatTime=null,this.heartbeatTimeout=6e3,this.connectionCheckTimer=null,this.connectionCheckInterval=2e4,this.forceReconnectTimer=null,this.forceReconnectInterval=6e4,this.visibilityChangeHandler=null,this.beforeUnloadHandler=null,this.pageHideHandler=null,this.popstateHandler=null,this.isPageVisible=!0,this.isRouteChanging=!1,this.routeListenerSetup=!1,this.currentPath="undefined"!==typeof window?window.location.pathname:"",this.setupVisibilityListener(),this.setupNavigationListener()}setupVisibilityListener(){"undefined"!==typeof document&&(this.visibilityChangeHandler=()=>{this.isPageVisible=!document.hidden,!document.hidden&&this.isInitialized?(console.log("🔧 [WebSocketManager] 页面重新可见，检查连接状态"),setTimeout(()=>{this.checkConnectionAfterVisibilityChange()},1e3)):document.hidden&&console.log("🔧 [WebSocketManager] 页面变为不可见，保持连接但减少活动")},document.addEventListener("visibilitychange",this.visibilityChangeHandler),this.beforeUnloadHandler=e=>{console.log("🔧 [WebSocketManager] 页面即将卸载，保持连接状态")},window.addEventListener("beforeunload",this.beforeUnloadHandler),this.pageHideHandler=()=>{console.log("🔧 [WebSocketManager] 页面隐藏，保持连接")},window.addEventListener("pagehide",this.pageHideHandler))}setupNavigationListener(){if("undefined"===typeof window)return;this.popstateHandler=e=>{const t=window.location.pathname;t!==this.currentPath&&(console.log("🔄 [WebSocketManager] 检测到导航变化:",this.currentPath,"->",t),this.handleRouteChange(this.currentPath,t),this.currentPath=t)},window.addEventListener("popstate",this.popstateHandler);const e=history.pushState,t=history.replaceState;history.pushState=(...t)=>{const n=t[2]||window.location.pathname;return n!==this.currentPath&&(console.log("🔄 [WebSocketManager] 检测到pushState导航:",this.currentPath,"->",n),this.handleRouteChange(this.currentPath,n),this.currentPath=n),e.apply(history,t)},history.replaceState=(...e)=>{const n=e[2]||window.location.pathname;return n!==this.currentPath&&(console.log("🔄 [WebSocketManager] 检测到replaceState导航:",this.currentPath,"->",n),this.handleRouteChange(this.currentPath,n),this.currentPath=n),t.apply(history,e)},console.log("🔧 [WebSocketManager] 导航监听器已设置")}handleRouteChange(e,t){this.isRouteChanging=!0,this.socket&&this.isConnected&&this.socket.emit("route_changing",{from:e,to:t,timestamp:(new Date).toISOString()}),setTimeout(()=>{console.log("🔄 [WebSocketManager] 导航完成，重置标识"),this.isRouteChanging=!1,!this.isInitialized||this.isConnected&&this.socket&&!this.socket.disconnected||(console.log("🔄 [WebSocketManager] 导航后检测到连接断开，重新连接"),setTimeout(()=>{this.init()},1e3))},1e3)}checkConnectionAfterVisibilityChange(){this.isConnected&&this.socket&&!this.socket.disconnected?(console.log("✅ [WebSocketManager] 页面重新可见时连接正常"),this.socket.emit("heartbeat",{timestamp:(new Date).toISOString(),clientType:"websocket_manager",reason:"visibility_check"})):(console.log("🔄 [WebSocketManager] 页面重新可见时发现连接已断开，尝试重连"),this.handleConnectionError(new Error("页面重新可见时连接已断开")))}scheduleReconnectWhenVisible(){if(console.log("🔄 [WebSocketManager] 安排页面可见时重连"),this.isPageVisible)return console.log("🔄 [WebSocketManager] 页面已可见，立即重连"),void this.scheduleReconnect();const e=()=>{this.isPageVisible&&this.isInitialized?(console.log("🔄 [WebSocketManager] 页面变为可见，开始重连"),this.scheduleReconnect()):this.isInitialized&&setTimeout(e,2e3)};setTimeout(e,2e3)}async init(){const e=o.A.getters["auth/isAuthenticated"];return console.log("🔧 [WebSocketManager] 初始化WebSocket连接...",{isAuthenticated:e}),this.isInitialized&&this.isConnected?(console.log("🔧 [WebSocketManager] WebSocket管理器已初始化且连接正常"),this.connectionPromise):(console.log("🔧 [WebSocketManager] 初始化WebSocket管理器..."),this.isInitialized=!0,this.connectionPromise?(console.log("🔧 [WebSocketManager] 连接Promise已存在，返回现有Promise"),this.connectionPromise):(this.connectionPromise=this.connect(),this.connectionPromise))}async connect(){try{console.log("🔧 [WebSocketManager] 建立WebSocket连接..."),this.cleanup();const e=(0,i.getWebSocketUrl)();console.log("🔧 [WebSocketManager] 连接到WebSocket服务器:",e||"使用代理模式");const t={transports:["websocket"],timeout:3e4,reconnection:!1,forceNew:!1,upgrade:!1,autoConnect:!0,pingTimeout:12e4,pingInterval:3e4,query:{clientType:"web_persistent",routeAware:"true"}};this.socket=e?(0,s.Ay)(e,t):(0,s.Ay)(t);const n=setTimeout(()=>{console.error("WebSocket连接超时"),this.handleConnectionError(new Error("连接超时"))},15e3);return new Promise((e,t)=>{this.socket.on("connect",()=>{clearTimeout(n),console.log("✅ [WebSocketManager] WebSocket连接成功"),this.isConnected=!0,this.reconnectAttempts=0,this.reconnectDelay=1e3,this.setupEventHandlers(),this.startHeartbeat(),this.startConnectionCheck(),this.startForceReconnectCheck(),o.A.dispatch("socket/connect",this.socket),o.A.commit("socket/SET_CONNECTED",!0),o.A.commit("socket/SET_SOCKET",this.socket),this.emitEvent("connection_established",{type:"websocket"}),this.registerWebClient(),setTimeout(()=>{this.requestDeviceStatusSync()},1e3),e()}),this.socket.on("connect_error",e=>{clearTimeout(n),console.error("❌ [WebSocketManager] WebSocket连接失败:",e.message),this.handleConnectionError(e),t(e)}),this.socket.on("disconnect",e=>{console.log("🔌 [WebSocketManager] WebSocket连接断开:",e),this.isConnected=!1,this.stopHeartbeat(),this.stopConnectionCheck(),this.stopForceReconnectCheck(),o.A.dispatch("socket/disconnect"),o.A.commit("socket/SET_CONNECTED",!1),this.emitEvent("connection_lost",{reason:e});const t="io client disconnect"===e||"client namespace disconnect"===e,n="io server disconnect"===e,s=this.isPageVisible,i=this.isRouteChanging||!1;if(console.log("🔍 [WebSocketManager] 断开连接分析:",{reason:e,isClientDisconnect:t,isServerDisconnect:n,isPageVisible:s,isRouteChange:i,isInitialized:this.isInitialized}),i)return void console.log("🔄 [WebSocketManager] 检测到路由切换，跳过重连，等待新页面初始化");if(n)return console.log("🔄 [WebSocketManager] 服务器主动断开，立即重连"),void setTimeout(()=>{this.isInitialized&&this.scheduleReconnect()},1e3);const c=this.isInitialized&&(!t||t&&s&&!i);if(c){console.log("🔄 [WebSocketManager] 检测到连接断开，准备重连...",{reason:e,pageVisible:s,isClientDisconnect:t});let n=1e3;t&&s?(n=500,console.log("🔄 [WebSocketManager] 检测到组件重载，快速重连")):s||(n=3e3,console.log("🔄 [WebSocketManager] 页面不可见，延迟重连")),setTimeout(()=>{this.isInitialized&&!this.isRouteChanging&&this.scheduleReconnect()},n)}else console.log("🔌 [WebSocketManager] 不进行重连",{reason:e,isInitialized:this.isInitialized,isClientDisconnect:t,pageVisible:s,isRouteChange:i})})})}catch(e){throw console.error("❌ [WebSocketManager] WebSocket连接异常:",e),this.handleConnectionError(e),e}}setupEventHandlers(){this.socket&&(this.socket.on("devices_list",e=>{console.log("WebSocket收到设备列表:",e),this.emitEvent("devices_list",e)}),this.socket.on("device_status_changed",e=>{console.log("WebSocket收到设备状态变化:",e),this.emitEvent("device_status_changed",e)}),this.socket.on("device_status_update",e=>{console.log("WebSocket收到设备状态更新:",e),this.emitEvent("device_status_update",e),o.A&&e.deviceId&&(o.A.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:e.status,last_seen:e.lastSeen||(new Date).toISOString()}}),console.log(`已更新store中设备 ${e.deviceId} 状态为: ${e.status}`))}),this.socket.on("device_status_changed",e=>{if(console.log("WebSocket收到设备状态变化:",e),this.emitEvent("device_status_changed",e),o.A&&e.deviceId){const t="device_health_warning"===e.type?"offline":"online";o.A.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:t,last_seen:e.lastSeen||(new Date).toISOString()}}),console.log(`健康检查：已更新store中设备 ${e.deviceId} 状态为: ${t}`)}}),this.socket.on("xiaohongshu_status_update",e=>{console.log("WebSocket收到小红书状态更新:",e),this.emitEvent("xiaohongshu_status_update",e)}),this.socket.on("xiaohongshu_function_tasks_stopped",e=>{console.log("WebSocket收到小红书功能任务停止:",e),this.emitEvent("xiaohongshu_function_tasks_stopped",e)}),this.socket.on("xiaohongshu_force_refresh_vuex",e=>{console.log("WebSocket收到小红书强制刷新Vuex:",e),this.emitEvent("xiaohongshu_force_refresh_vuex",e)}),this.socket.on("xiaohongshu_clear_function_state",e=>{console.log("WebSocket收到小红书清理功能状态:",e),this.emitEvent("xiaohongshu_clear_function_state",e)}),this.socket.on("xiaohongshu_all_tasks_stopped",e=>{console.log("WebSocket收到小红书所有任务停止:",e),this.emitEvent("xiaohongshu_all_tasks_stopped",e)}),this.socket.on("xiaohongshu_task_update",e=>{console.log("WebSocket收到小红书任务更新:",e),this.emitEvent("xiaohongshu_task_update",e)}),this.socket.on("xiaohongshu_debug_log",e=>{console.log("WebSocket收到小红书调试日志:",e),this.emitEvent("xiaohongshu_debug_log",e)}),this.socket.on("xiaohongshu_execution_completed",e=>{console.log("WebSocket收到小红书执行完成:",e),this.emitEvent("xiaohongshu_execution_completed",e)}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("WebSocket收到小红书实时状态:",e),this.emitEvent("xiaohongshu_realtime_status",e)}),this.socket.on("xianyu_realtime_status",e=>{console.log("WebSocket收到闲鱼实时状态:",e),this.emitEvent("xianyu_realtime_status",e)}),this.socket.on("server_shutdown",e=>{console.log("收到服务器关闭通知:",e),this.emitEvent("server_shutdown",e),this.isConnected=!1,this.isInitialized=!1}),this.socket.on("pong",()=>{console.log("💓 [WebSocketManager] 收到pong心跳响应"),this.lastHeartbeatTime=Date.now()}),this.socket.on("heartbeat_response",e=>{console.log("💓 [WebSocketManager] 收到服务器心跳响应:",e),this.lastHeartbeatTime=Date.now()}),this.socket.on("connect_error",e=>{console.error("❌ [WebSocketManager] 连接错误:",e),this.handleConnectionError(e)}),this.socket.on("reconnect_error",e=>{console.error("❌ [WebSocketManager] 重连错误:",e),this.handleConnectionError(e)}),this.socket.on("test_event",e=>{console.log("收到测试事件:",e),this.emitEvent("test_event",e)}))}startHeartbeat(){this.stopHeartbeat(),console.log("🔧 [WebSocketManager] 启动心跳机制，间隔:",this.heartbeatInterval+"ms"),this.sendHeartbeat(),this.heartbeatTimer=setInterval(()=>{this.sendHeartbeat()},this.heartbeatInterval)}sendHeartbeat(){if(this.socket&&this.isConnected&&!this.socket.disconnected)try{this.lastHeartbeatTime=Date.now(),this.socket.emit("ping"),this.socket.emit("heartbeat",{timestamp:(new Date).toISOString(),clientType:"websocket_manager",pageVisible:this.isPageVisible}),this.isPageVisible?console.log("💓 [WebSocketManager] 发送心跳（页面可见）"):Date.now()%6e4<this.heartbeatInterval&&console.log("💓 [WebSocketManager] 发送心跳（页面不可见）")}catch(e){console.error("❌ [WebSocketManager] 发送心跳失败:",e),this.handleConnectionError(e)}else console.warn("⚠️ [WebSocketManager] 心跳检查时发现连接已断开"),this.stopHeartbeat(),this.isInitialized&&this.isPageVisible&&(console.log("🔄 [WebSocketManager] 页面可见时检测到连接断开，尝试重连"),this.handleConnectionError(new Error("心跳检查时发现连接断开")))}stopHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null,console.log("🔧 [WebSocketManager] 心跳机制已停止"))}startConnectionCheck(){this.stopConnectionCheck(),console.log("🔧 [WebSocketManager] 启动连接状态检查，间隔:",this.connectionCheckInterval+"ms"),this.connectionCheckTimer=setInterval(()=>{this.checkConnectionHealth()},this.connectionCheckInterval)}stopConnectionCheck(){this.connectionCheckTimer&&(clearInterval(this.connectionCheckTimer),this.connectionCheckTimer=null,console.log("🔧 [WebSocketManager] 连接状态检查已停止"))}startForceReconnectCheck(){this.stopForceReconnectCheck(),console.log("🔧 [WebSocketManager] 启动强制重连检查，间隔:",this.forceReconnectInterval+"ms"),this.forceReconnectTimer=setInterval(()=>{this.forceConnectionCheck()},this.forceReconnectInterval)}stopForceReconnectCheck(){this.forceReconnectTimer&&(clearInterval(this.forceReconnectTimer),this.forceReconnectTimer=null,console.log("🔧 [WebSocketManager] 强制重连检查已停止"))}forceConnectionCheck(){if(console.log("🔍 [WebSocketManager] 执行强制连接检查..."),this.isInitialized)if(this.isConnected&&this.socket&&!this.socket.disconnected)try{this.socket.emit("force_connection_test",{timestamp:(new Date).toISOString(),clientType:"websocket_manager"}),console.log("✅ [WebSocketManager] 强制连接检查通过")}catch(e){console.error("❌ [WebSocketManager] 强制连接测试失败:",e),this.forceReconnect()}else console.warn("⚠️ [WebSocketManager] 强制检查发现连接断开，立即重连"),this.forceReconnect();else console.log("⚠️ [WebSocketManager] 未初始化，跳过强制检查")}checkConnectionHealth(){if(!this.socket||!this.isConnected)return console.warn("⚠️ [WebSocketManager] 连接健康检查：连接已断开，尝试重连"),void(this.isInitialized&&this.handleConnectionError(new Error("连接健康检查发现连接断开")));if(this.lastHeartbeatTime){const e=Date.now()-this.lastHeartbeatTime,t=this.heartbeatTimeout+this.heartbeatInterval;if(e>t)return console.error("❌ [WebSocketManager] 心跳超时，强制重连",{timeSinceLastHeartbeat:e,heartbeatTimeout:t,lastHeartbeat:new Date(this.lastHeartbeatTime).toISOString()}),void this.handleConnectionError(new Error("心跳超时"))}if(this.socket.disconnected)return console.error("❌ [WebSocketManager] Socket已断开，但状态未更新，强制重连"),void this.handleConnectionError(new Error("Socket状态不一致"));try{this.socket.emit("connection_test",{timestamp:(new Date).toISOString(),clientType:"websocket_manager"}),console.log("✅ [WebSocketManager] 连接健康检查通过，已发送连接测试")}catch(e){console.error("❌ [WebSocketManager] 连接测试失败:",e),this.handleConnectionError(e)}}handleConnectionError(e){console.error("🚨 [WebSocketManager] 连接错误:",e.message||e),this.isConnected=!1,this.stopHeartbeat(),this.stopConnectionCheck(),o.A.dispatch("socket/disconnect"),o.A.commit("socket/SET_CONNECTED",!1),this.emitEvent("connection_error",{error:e.message||e}),this.connectionPromise=null,this.isInitialized?(console.log("🔄 [WebSocketManager] 连接错误，立即安排重连..."),setTimeout(()=>{this.scheduleReconnect()},500)):console.log("⚠️ [WebSocketManager] 连接错误，但未初始化，不进行重连")}scheduleReconnect(){if(this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectAttempts>=this.maxReconnectAttempts)return console.error("❌ [WebSocketManager] 重连次数已达上限，重置重连计数器并继续尝试"),this.reconnectAttempts=0,void setTimeout(()=>{this.scheduleReconnect()},6e4);this.reconnectAttempts++;const e=Math.min(this.reconnectDelay*Math.pow(1.5,this.reconnectAttempts-1),1e4);console.log(`🔄 [WebSocketManager] 安排重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${e}ms后重试`),this.emitEvent("reconnect_attempt",{attempt:this.reconnectAttempts,maxAttempts:this.maxReconnectAttempts,delay:e}),this.reconnectTimer=setTimeout(async()=>{try{console.log(`🔄 [WebSocketManager] 开始第${this.reconnectAttempts}次重连尝试`),this.cleanup(),this.connectionPromise=this.connect(),await this.connectionPromise,console.log("✅ [WebSocketManager] 重连成功，重置重连计数器"),this.reconnectAttempts=0,this.emitEvent("reconnect_success",{attempt:this.reconnectAttempts})}catch(e){console.error(`❌ [WebSocketManager] 第${this.reconnectAttempts}次重连失败:`,e),this.emitEvent("reconnect_failed",{attempt:this.reconnectAttempts,error:e.message}),setTimeout(()=>{this.scheduleReconnect()},2e3)}},e)}cleanup(){console.log("🔧 [WebSocketManager] 清理连接资源"),this.stopHeartbeat(),this.stopConnectionCheck(),this.stopForceReconnectCheck(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.isConnected=!1,this.lastHeartbeatTime=null}cleanupEventListeners(){"undefined"!==typeof document&&this.visibilityChangeHandler&&(document.removeEventListener("visibilitychange",this.visibilityChangeHandler),this.visibilityChangeHandler=null),"undefined"!==typeof window&&this.beforeUnloadHandler&&(window.removeEventListener("beforeunload",this.beforeUnloadHandler),this.beforeUnloadHandler=null),"undefined"!==typeof window&&this.pageHideHandler&&(window.removeEventListener("pagehide",this.pageHideHandler),this.pageHideHandler=null),"undefined"!==typeof window&&this.popstateHandler&&(window.removeEventListener("popstate",this.popstateHandler),this.popstateHandler=null),console.log("🔧 [WebSocketManager] 事件监听器清理完成")}disconnect(){console.log("🔌 [WebSocketManager] 主动断开WebSocket连接"),this.isInitialized=!1,this.connectionPromise=null,this.cleanup(),this.cleanupEventListeners(),o.A.dispatch("socket/disconnect"),o.A.commit("socket/SET_CONNECTED",!1)}softDisconnect(){console.log("🔌 [WebSocketManager] 软断开 - 保持连接但暂停活动"),this.stopHeartbeat(),this.stopConnectionCheck(),this.stopForceReconnectCheck(),console.log("🔌 [WebSocketManager] 软断开完成，连接将在需要时自动恢复")}resumeConnection(){console.log("🔌 [WebSocketManager] 恢复连接活动"),this.isInitialized&&this.isConnected&&this.socket&&!this.socket.disconnected?(console.log("🔌 [WebSocketManager] 连接正常，恢复心跳和检查"),this.startHeartbeat(),this.startConnectionCheck(),this.startForceReconnectCheck()):this.isInitialized&&(console.log("🔌 [WebSocketManager] 连接异常，重新建立连接"),this.init())}emit(e,t){return this.socket&&this.isConnected?(this.socket.emit(e,t),!0):(console.warn("WebSocket未连接，无法发送消息:",e),!1)}on(e,t){this.eventHandlers.has(e)||this.eventHandlers.set(e,new Set),this.eventHandlers.get(e).add(t)}off(e,t){this.eventHandlers.has(e)&&(t?this.eventHandlers.get(e).delete(t):this.eventHandlers.get(e).clear())}emitEvent(e,t){this.eventHandlers.has(e)&&this.eventHandlers.get(e).forEach(n=>{try{n(t)}catch(s){console.error(`事件处理器执行失败 [${e}]:`,s)}})}getConnectionStatus(){return{isConnected:this.isConnected,isInitialized:this.isInitialized,reconnectAttempts:this.reconnectAttempts,type:"websocket"}}forceReconnect(){return console.log("强制重连WebSocket"),this.reconnectAttempts=0,this.connectionPromise=null,this.cleanup(),this.init()}registerWebClient(){if(!this.isConnected||!this.socket)return;const e=o.A.getters["auth/user"],t=e?e.id:"anonymous";this.socket.emit("web_client_connect",{userId:t,username:e?e.username:"anonymous",clientType:"websocket_manager",page:"global"}),console.log("📡 [WebSocketManager] 已注册为用户",t,"的全局客户端")}requestDeviceStatusSync(){console.log("🔄 [WebSocketManager] 请求设备状态同步..."),this.isConnected&&this.socket&&this.socket.emit("request_device_status_sync")}getConnectionStatus(){return{isConnected:this.isConnected,socket:this.socket,type:this.isConnected?"websocket":"disconnected"}}forceReconnect(){console.log("🔄 [WebSocketManager] 强制重连..."),this.cleanup(),this.reconnectAttempts=0,setTimeout(()=>{this.init()},1e3)}ensureConnected(){return!(!this.isConnected||!this.socket||this.socket.disconnected)||(console.log("🔄 [WebSocketManager] 检测到连接断开，开始重连..."),this.forceReconnect(),!1)}send(e,t){if(!this.isConnected||!this.socket||this.socket.disconnected)return console.warn("⚠️ [WebSocketManager] WebSocket未连接，无法发送消息:",e,t),this.ensureConnected(),!1;try{return this.socket.emit(e,t),!0}catch(n){return console.error("❌ [WebSocketManager] 发送消息失败:",n),this.handleConnectionError(n),!1}}}const a=new c;function r(){return a.init()}function l(){return a}async function u(){return a.isConnected||(console.log("🔧 [WebSocketManager] 连接未建立，尝试初始化..."),await a.init()),a.isConnected}},9381:function(e,t,n){"use strict";function s(){const e=window.location.port,t=window.location.hostname;console.log("🔧 [ServerConfig] 获取服务器URL，当前环境:",{NODE_ENV:"production",hostname:t,port:e,protocol:window.location.protocol,fullUrl:window.location.href});const n="8080"===e||"8081"===e,s="localhost"===t||"127.0.0.1"===t;if(n)return console.log("🔧 [ServerConfig] 检测到开发服务器端口，强制使用代理模式"),"";if(s)return console.log("🔧 [ServerConfig] 检测到localhost访问，使用代理模式"),"";const o="https:"===window.location.protocol?"https":"http",i=t,c=e||("https"===o?"443":"80"),a="3002"===c?c:"3002",r=`${o}://${i}:${a}`;return console.log("🔧 [ServerConfig] 生成的服务器URL:",r),r}function o(){return s()}function i(){return s()}function c(){return!1}function a(){return c()&&"localhost"===window.location.hostname}function r(){return{isDevelopment:c(),isProxyAccess:a(),hostname:window.location.hostname,port:window.location.port,protocol:window.location.protocol,serverUrl:s(),webSocketUrl:o(),apiBaseUrl:i()}}n.r(t),n.d(t,{getApiBaseUrl:function(){return i},getEnvironmentInfo:function(){return r},getServerUrl:function(){return s},getWebSocketUrl:function(){return o},isDevelopment:function(){return c},isProxyAccess:function(){return a}}),c()&&console.log("🔧 服务器配置信息:",r())}},t={};function n(s){var o=t[s];if(void 0!==o)return o.exports;var i=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.m=e,function(){n.amdO={}}(),function(){var e=[];n.O=function(t,s,o,i){if(!s){var c=1/0;for(u=0;u<e.length;u++){s=e[u][0],o=e[u][1],i=e[u][2];for(var a=!0,r=0;r<s.length;r++)(!1&i||c>=i)&&Object.keys(n.O).every(function(e){return n.O[e](s[r])})?s.splice(r--,1):(a=!1,i<c&&(c=i));if(a){e.splice(u--,1);var l=o();void 0!==l&&(t=l)}}return t}i=i||0;for(var u=e.length;u>0&&e[u-1][2]>i;u--)e[u]=e[u-1];e[u]=[s,o,i]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce(function(t,s){return n.f[s](e,t),t},[]))}}(),function(){n.u=function(e){return"static/js/"+e+"."+{145:"0a263a07",205:"9fbfa997",258:"70dd8370",384:"381a0574",400:"869521d2",414:"dc7b00a5",431:"cae588eb",483:"8455dec2",511:"319538db",534:"0fb2643f",547:"55091115",578:"aeec9065",688:"ec8dd40b",838:"f722378b",928:"317cec88",942:"daccab32"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"static/css/"+e+"."+{145:"99b03665",205:"03d09fe6",258:"55756b79",384:"5f4c38d7",400:"a4d6a6f4",414:"6ab74a50",431:"1205f524",483:"31d81b1a",511:"d3950945",547:"46d29503",578:"9ba40d0b",688:"deeb1124",838:"485b25a4",928:"8223e325",942:"372386ca"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="autojs-web-control:";n.l=function(s,o,i,c){if(e[s])e[s].push(o);else{var a,r;if(void 0!==i)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==s||d.getAttribute("data-webpack")==t+i){a=d;break}}a||(r=!0,a=document.createElement("script"),a.charset="utf-8",a.timeout=120,n.nc&&a.setAttribute("nonce",n.nc),a.setAttribute("data-webpack",t+i),a.src=s),e[s]=[o];var g=function(t,n){a.onerror=a.onload=null,clearTimeout(h);var o=e[s];if(delete e[s],a.parentNode&&a.parentNode.removeChild(a),o&&o.forEach(function(e){return e(n)}),t)return t(n)},h=setTimeout(g.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=g.bind(null,a.onerror),a.onload=g.bind(null,a.onload),r&&document.head.appendChild(a)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,s,o,i){var c=document.createElement("link");c.rel="stylesheet",c.type="text/css",n.nc&&(c.nonce=n.nc);var a=function(n){if(c.onerror=c.onload=null,"load"===n.type)o();else{var s=n&&n.type,a=n&&n.target&&n.target.href||t,r=new Error("Loading CSS chunk "+e+" failed.\n("+s+": "+a+")");r.name="ChunkLoadError",r.code="CSS_CHUNK_LOAD_FAILED",r.type=s,r.request=a,c.parentNode&&c.parentNode.removeChild(c),i(r)}};return c.onerror=c.onload=a,c.href=t,s?s.parentNode.insertBefore(c,s.nextSibling):document.head.appendChild(c),c},t=function(e,t){for(var n=document.getElementsByTagName("link"),s=0;s<n.length;s++){var o=n[s],i=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(i===e||i===t))return o}var c=document.getElementsByTagName("style");for(s=0;s<c.length;s++){o=c[s],i=o.getAttribute("data-href");if(i===e||i===t)return o}},s=function(s){return new Promise(function(o,i){var c=n.miniCssF(s),a=n.p+c;if(t(c,a))return o();e(s,a,null,o,i)})},o={524:0};n.f.miniCss=function(e,t){var n={145:1,205:1,258:1,384:1,400:1,414:1,431:1,483:1,511:1,547:1,578:1,688:1,838:1,928:1,942:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=s(e).then(function(){o[e]=0},function(t){throw delete o[e],t}))}}}(),function(){var e={524:0};n.f.j=function(t,s){var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)s.push(o[2]);else{var i=new Promise(function(n,s){o=e[t]=[n,s]});s.push(o[2]=i);var c=n.p+n.u(t),a=new Error,r=function(s){if(n.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var i=s&&("load"===s.type?"missing":s.type),c=s&&s.target&&s.target.src;a.message="Loading chunk "+t+" failed.\n("+i+": "+c+")",a.name="ChunkLoadError",a.type=i,a.request=c,o[1](a)}};n.l(c,r,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,s){var o,i,c=s[0],a=s[1],r=s[2],l=0;if(c.some(function(t){return 0!==e[t]})){for(o in a)n.o(a,o)&&(n.m[o]=a[o]);if(r)var u=r(n)}for(t&&t(s);l<c.length;l++)i=c[l],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(u)},s=self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=n.O(void 0,[504],function(){return n(3019)});s=n.O(s)})();